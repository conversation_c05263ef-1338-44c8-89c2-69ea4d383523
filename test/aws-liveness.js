const {
  app, mongoose, validImagePath, validVideoPath,
} = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const { notifs, reset, waitFor } = require('./stub');
const request = require('supertest');
const temp = require('temp').track();
const fs = require('fs');
const sinon = require('sinon');
const stub = require('./stub');
const User = require('../models/user');
const constants = require('../lib/constants');

describe('aws liveness', () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.100' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.pictures.length).to.equal(1);

    res = await request(app)
      .post('/v1/aws-liveness/session')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.sessionId).to.equal('mockSessionId');
    expect(res.body.region).to.equal('us-east-1');
  });

  it('pass all checks', async () => {
    res = await request(app)
      .get('/v1/aws-liveness/session/results')
      .query({ sessionId: 'mockSessionId' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('liveness confidence below threshold', async () => {
    fakeRekognition.getFaceLivenessSessionResults = function(params) {
      const impl = function (resolve, reject) {
        console.log('Fake Rekognition : getFaceLivenessSessionResults override')
        resolve({
          Confidence: 50,
          Status: 'SUCCEEDED',
          ReferenceImage: {
            S3Object: {
              Name: 'mock1.jpg',
            },
          },
          AuditImages: [
            {
              S3Object: {
                Name: 'mock2.jpg',
              },
            },
            {
              S3Object: {
                Name: 'mock3.jpg',
              },
            },
          ],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    }

    res = await request(app)
      .get('/v1/aws-liveness/session/results')
      .query({ sessionId: 'mockSessionId' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Photo unclear');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Photo unclear');
  });

  it('fail face comparison', async () => {
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ UnmatchedFaces: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .get('/v1/aws-liveness/session/results')
      .query({ sessionId: 'mockSessionId' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');

    res = await request(app)
      .get('/v1/user/verificationStatus')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');
  });

});

