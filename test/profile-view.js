const {
  app, mongoose, validImagePath, validVideoPath,
} = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const moment = require('moment');
const { notifs, reset, waitFor } = require('./stub');
const iapHelper = require('./helper/iap');
const constants = require('../lib/constants');
const metricsLib = require('../lib/metrics');
const userLib = require('../lib/user');
const User = require('../models/user');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const ProfileView = require('../models/profile-view');
const Interest = require('../models/interest');


it('should exclude unverified users from profile views', async () => {
  constants.hideUnverifiedUsers.restore();
  sinon.stub(constants, 'hideUnverifiedUsers').returns(true);

  for (let uid = 0; uid < 2; uid++) {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);
    expect(res.status).to.equal(200);
  }

  let res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', 1)
    .send({
      firstName: `name1`,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/handle')
    .set('authorization', 1)
    .send({
      handle: `handle1`,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/personality')
    .set('authorization', 1)
    .send({
      mbti: 'ESTJ',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/birthday')
    .set('authorization', 1)
    .send({
      year: new Date().getFullYear() - 31,
      month: 1,
      day: 1,
    });

  res = await request(app)
    .put('/v1/user/enneagram')
    .set('authorization', 1)
    .send({ enneagram: '1w9' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 1)
    .send({ gender: 'male' });
  expect(res.status).to.equal(200);

  // mock upload two pictures
  let user = await User.findOne({ _id: '1' });
  user.pictures.push('picture0');
  user.pictures.push('picture1');
  res = await user.save();

  // no views initially
  res = await request(app)
    .get('/v1/profile-view')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numProfileViews).to.equal(0);

  // user 1 views user 0 profile
  res = await request(app)
    .get('/v1/user/profileDetails')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);

  // set user 0 premium
  user = await User.findById('0');
  user.premiumExpiration = Date.now() + ********;
  res = await user.save();

  res = await request(app)
    .get('/v1/profile-view')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numProfileViews).to.equal(1);
  expect(res.body.views).to.eql([]);

  // verify user 1
  user = await User.findById('1');
  user.verification = { status: 'verified' };
  await user.save();

  res = await request(app)
    .get('/v1/profile-view')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numProfileViews).to.equal(1);
  expect(res.body.views.length).to.equal(1);
  expect(res.body.views[0].user).to.eql({
    "_id": "1",
    "enneagram": "1w9",
    "firstName": "name1",
    "handle": "handle1",
    "horoscope": "Capricorn",
    "personality": {
      "mbti": "ESTJ"
    },
    "picture": "picture0",
    "age": 31,
    "gender": "male",
    "verified": true,
  });
});

describe('profile view', async () => {

  beforeEach(async () => {
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.10.0',
        });
      expect(res.status).to.equal(200);
    }
  });

  it('basic usage', async () => {
    // set user 1 fields
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 1)
      .send({
        firstName: `name1`,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/handle')
      .set('authorization', 1)
      .send({
        handle: `handle1`,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    res = await request(app)
      .put('/v1/user/enneagram')
      .set('authorization', 1)
      .send({ enneagram: '1w9' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);
    // mock upload two pictures
    user = await User.findOne({ _id: '1' });
    user.pictures.push('picture0');
    user.pictures.push('picture1');
    res = await user.save();

    // no views initially
    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.numProfileViews).to.equal(0);

    // user 1 views user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.numProfileViews).to.equal(1);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user).to.eql({
      "_id": "1",
      "enneagram": "1w9",
      "firstName": "name1",
      "handle": "handle1",
      "horoscope": "Capricorn",
      "personality": {
        "mbti": "ESTJ"
      },
      "picture": "picture0",
      "age": 31,
      "gender": "male",
      "verified": false,
    });
  });

  it('numProfileViews and pagination', async () => {
    sinon.stub(constants, 'getPageSize').returns(1);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(0);

    // user 1 views user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(1);

    // user 2 views user 0 profile
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 2)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(2);

    // user 3 views user 0 profile
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 3)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(3);

    // ban user 3
    user = await User.findById('3');
    user.shadowBanned = true;
    res = await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(3);

    // recount doesn't affect view count
    user = await User.findById('0');
    user.metrics.numFollowersUpdatedAt = undefined;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(3);

    // not premium - can see first page, but not second page
    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.numProfileViews).to.equal(3);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('2');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(403);

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.numProfileViews).to.equal(3);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('2');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('1');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);

    // limit profile views to 7 days
    doc = await ProfileView.findOne({from: '2', to: '0'});
    doc.createdAt = moment().subtract(8, 'days').toDate();
    await doc.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('1');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);
  });

  it('profiles i viewed', async () => {
    sinon.stub(constants, 'getPageSize').returns(1);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    // view several profiles
    for (let i = 1; i <= 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/user/profileDetails')
        .set('authorization', 0)
        .query({ user: i.toString() });
      expect(res.status).to.equal(200);
    }

    // ban user 3
    user = await User.findById('3');
    user.shadowBanned = true;
    res = await user.save();

    // not premium - can see first page, but not second page
    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('2');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(403);

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('2');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('1');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);

    // limit profile views to 7 days
    doc = await ProfileView.findOne({from: '0', to: '2'});
    doc.createdAt = moment().subtract(8, 'days').toDate();
    await doc.save();

    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('1');

    beforeDate = res.body.views[0].createdAt;
    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
      .query({ beforeDate })
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);
  });

  it('page size larger than 1', async () => {
    sinon.stub(constants, 'getPageSize').returns(3);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    // view several profiles
    for (let i = 1; i <= 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/user/profileDetails')
        .set('authorization', 0)
        .query({ user: i.toString() });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(3);
    expect(res.body.views[0].user._id).to.equal('3');
    expect(res.body.views[1].user._id).to.equal('2');
    expect(res.body.views[2].user._id).to.equal('1');
  });

  it('hide profile views', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.hideProfileViews).to.equal();

    // cannot set because not premium
    res = await request(app)
      .put('/v1/user/hideProfileViews')
      .set('authorization', 1)
      .send({ hideProfileViews: true })
    expect(res.status).to.equal(403);

    // set user 1 premium
    user = await User.findById('1');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .put('/v1/user/hideProfileViews')
      .set('authorization', 1)
      .send({ hideProfileViews: true })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.hideProfileViews).to.equal(true);

    // user 1 views user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // view is counted but hidden
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(1);

    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);

    // user 1 can see profiles viewed
    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('0');

    // user 0 hiding profile views should not affect user 1 seeing viewed profiles
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .put('/v1/user/hideProfileViews')
      .set('authorization', 0)
      .send({ hideProfileViews: true })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('0');

    // disable setting - view should be visible now
    res = await request(app)
      .put('/v1/user/hideProfileViews')
      .set('authorization', 1)
      .send({ hideProfileViews: false })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('1');
  });

  it('delete account', async () => {
    // user 1 views user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // delete user 1
    user = await User.findById('1');
    await userLib.deleteAccount(user);

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);

    views = await ProfileView.find();
    expect(views.length).to.equal(0);
  });

  it('save profile view on swiping', async () => {
    // user 1 views user 0 profile
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.numProfileViews).to.equal(1);

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user._id).to.equal('1');
  });

  it('views from admins should be hidden', async () => {
    // set user 1 admin
    user = await User.findOne({ _id: '1' });
    user.admin = true;
    res = await user.save();

    // user 1 views user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);
  });

  it('views from admins should be hidden', async () => {
    // user 1 views user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // set user 1 admin
    user = await User.findOne({ _id: '1' });
    user.admin = true;
    res = await user.save();

    // set user 0 premium
    user = await User.findById('0');
    user.premiumExpiration = Date.now() + ********;
    res = await user.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(0);
  });

});

it('notify_profile_view', async () => {
  const clock = sinon.useFakeTimers();

  for (let uid = 0; uid < 10; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/fcmToken')
    .set('authorization', 0)
    .send({
      fcmToken: 'token',
    });
  expect(res.status).to.equal(200);

  // not premium, 1st week, max 1 per day
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 1)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(1);
  expect(notifs.recent.token).to.equal('token');
  expect(notifs.recent.notification.title).to.equal('Someone viewed your profile 👀');
  expect(notifs.recent.notification.body).to.equal('See who.');
  expect(notifs.recent.data).to.eql({ premiumPopup: 'seeWhoViewed' });
  reset();

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 2)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(0);
  reset();

  clock.tick(1*60*60*1000); // 1 hour

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 3)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(0);
  reset();

  clock.tick(24*60*60*1000); // 24 hours

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 4)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(1);
  reset();

  // not premium, after 1st week, max 1 per week
  clock.tick(7*24*60*60*1000); // 7 days

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 5)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(1);
  reset();

  clock.tick(1*24*60*60*1000); // 1 day

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 6)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(0);
  reset();

  clock.tick(7*24*60*60*1000); // 7 days

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 7)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(1);
  reset();

  // premium, no notification
  clock.tick(30*24*60*60*1000); // 30 days

  user = await User.findById('0');
  user.premiumExpiration = Date.now() + ********;
  await user.save();

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 8)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  expect(notifs.numSent).to.equal(0);
  reset();

  clock.restore();
});

describe('profile view with tags and interestNames', async () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.13.104',
        });
      expect(res.status).to.equal(200);
    }
  });

  it('GET /v1/profile-view includes tags and interestNames', async () => {
    let user0 = await User.findById('0');
    user0.gender = 'male';
    await user0.save();

    let user1 = await User.findById('1');
    user1.firstName = 'name1';
    user1.handle = 'handle1';
    user1.gender = 'female';
    user1.scores.likeRatio = 0.9;
    user1.scores.numActionsReceived = 10;
    user1.metrics.numLikesReceived = 9;
    user1.metrics.numActionsReceived = 35;
    user1.metrics.lastSeen = new Date();
    user1.pictures = ['picture0', 'picture1'];
    await user1.save();

    let user2 = await User.findById('2');
    user2.firstName = 'name2';
    user2.handle = 'handle2';
    user2.gender = 'male';
    user2.scores.likeRatio = 0.15;
    user2.metrics.numActionsReceived = 40;
    user2.metrics.lastSeen = new Date(Date.now() - 10 * 60 * 1000);
    user2.pictures = ['picture0', 'picture1'];
    await user2.save();

    await Interest.insertMany([
      { interest: '#gaming', name: 'gaming', libCategory: 'hobbies', numFollowers: 10 },
      { interest: '#music', name: 'music', libCategory: 'arts', numFollowers: 15 },
      { interest: '#movies', name: 'movies', libCategory: 'entertainment', numFollowers: 8 },
      { interest: '#travel', name: 'travel', libCategory: 'lifestyle', numFollowers: 12 },
    ]);

    // Set interests for user 0 (requesting user) for Mutual Interests tag
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({ interestNames: ['gaming', 'travel'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 1)
      .send({ interestNames: ['gaming', 'music'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 2)
      .send({ interestNames: ['gaming', 'movies'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 0)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({ mbti: 'ISTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 2)
      .send({ mbti: 'ENTP' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 40.752449,
        longitude: -73.977855,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 40.752449,
        longitude: -73.977855,
      });
    expect(res.status).to.equal(200);

    // User 1 and 2 view user 0 profile
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 2)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // Set user 0 premium to see profile views
    user0 = await User.findById('0');
    user0.premiumExpiration = Date.now() + ********;
    await user0.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.numProfileViews).to.equal(2);
    expect(res.body.views.length).to.equal(2);

    const view1 = res.body.views.find(v => v.user._id === '1');
    expect(view1).to.exist;
    expect(view1.user.interestNames).to.deep.equal(['gaming', 'music']);
    expect(view1.user.tags).to.include('Active Now');
    expect(view1.user.tags).to.include('Mutual Interests'); // gaming is common
    expect(view1.user.tags).to.include('Nearby');
    expect(view1.user.tags).to.include('Compatible Personality'); // ISTJ is compatible with ESTJ
    expect(view1.user.tags).to.include('New Soul');
    expect(view1.user.tags).to.include('Top Soul'); // high like ratio for female

    // Check second view (user 2) - should have tags and interestNames
    const view2 = res.body.views.find(v => v.user._id === '2');
    expect(view2).to.exist;
    expect(view2.user.interestNames).to.deep.equal(['gaming', 'movies']);
    expect(view2.user.tags).to.include('Active Now');
    expect(view2.user.tags).to.include('Mutual Interests'); // gaming is common
    expect(view2.user.tags).to.include('New Soul');
    expect(view2.user.tags).to.not.include('Compatible Personality'); // ENTP not compatible with ESTJ
    expect(view2.user.tags).to.not.include('Top Soul'); // male with lower like ratio
  });

  it('GET /v1/profile-view/profiles-i-viewed includes tags and interestNames', async () => {
    let user0 = await User.findById('0');
    user0.gender = 'male';
    await user0.save();

    let user1 = await User.findById('1');
    user1.firstName = 'name1';
    user1.handle = 'handle1';
    user1.gender = 'female';
    user1.scores.likeRatio = 0.8;
    user1.metrics.numActionsReceived = 50;
    user1.metrics.lastSeen = new Date();
    user1.pictures = ['picture0', 'picture1'];
    await user1.save();

    let user2 = await User.findById('2');
    user2.firstName = 'name2';
    user2.handle = 'handle2';
    user2.gender = 'male';
    user2.scores.likeRatio = 0.15;
    user2.metrics.numActionsReceived = 45;
    user2.metrics.lastSeen = new Date(Date.now() - 5 * 60 * 1000);
    user2.pictures = ['picture0', 'picture1'];
    await user2.save();

    await Interest.insertMany([
      { interest: '#photography', name: 'photography', libCategory: 'arts', numFollowers: 12 },
      { interest: '#travel', name: 'travel', libCategory: 'lifestyle', numFollowers: 20 },
      { interest: '#fitness', name: 'fitness', libCategory: 'health', numFollowers: 18 },
    ]);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({ interestNames: ['photography', 'travel'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 1)
      .send({ interestNames: ['photography', 'fitness'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 2)
      .send({ interestNames: ['travel', 'fitness'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 0)
      .send({ mbti: 'INFP' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({ mbti: 'ENFJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 2)
      .send({ mbti: 'ESTJ' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 37.7749,
        longitude: -122.4194,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 37.7749,
        longitude: -122.4194,
      });
    expect(res.status).to.equal(200);

    // User 0 views user 1 and 2 profiles
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '2' });
    expect(res.status).to.equal(200);

    // Set user 0 premium to see profiles they viewed
    user0 = await User.findById('0');
    user0.premiumExpiration = Date.now() + ********;
    await user0.save();

    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(2);

    // Check first viewed profile (user 2, most recent)
    const viewedProfile1 = res.body.views.find(v => v.user._id === '2');
    expect(viewedProfile1).to.exist;
    expect(viewedProfile1.user.interestNames).to.deep.equal(['travel', 'fitness']);
    expect(viewedProfile1.user.tags).to.include('Active Now');
    expect(viewedProfile1.user.tags).to.include('Mutual Interests'); // travel is common
    expect(viewedProfile1.user.tags).to.include('New Soul');
    expect(viewedProfile1.user.tags).to.not.include('Compatible Personality'); // ESTJ not compatible with INFP
    expect(viewedProfile1.user.tags).to.not.include('Top Soul'); // male with lower like ratio

    // Check second viewed profile (user 1)
    const viewedProfile2 = res.body.views.find(v => v.user._id === '1');
    expect(viewedProfile2).to.exist;
    expect(viewedProfile2.user.interestNames).to.deep.equal(['photography', 'fitness']);
    expect(viewedProfile2.user.tags).to.include('Active Now');
    expect(viewedProfile2.user.tags).to.include('Mutual Interests'); // photography is common
    expect(viewedProfile2.user.tags).to.include('Nearby');
    expect(viewedProfile2.user.tags).to.include('Compatible Personality'); // ENFJ is compatible with INFP
    expect(viewedProfile2.user.tags).to.include('New Soul');
    expect(viewedProfile2.user.tags).to.include('Top Soul'); // high like ratio for female
  });

  it('older versions do not include tags and interestNames', async () => {
    // Test with older version (before 1.13.104)
    let user0 = await User.findById('0');
    user0.appVersion = '1.13.103';
    await user0.save();

    let user1 = await User.findById('1');
    user1.firstName = 'name1';
    user1.handle = 'handle1';
    user1.pictures = ['picture0', 'picture1'];
    await user1.save();

    await Interest.insertMany([
      { interest: '#books', name: 'books', libCategory: 'education', numFollowers: 5 },
    ]);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 1)
      .send({ interestNames: ['books'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // Set user 0 premium
    user0.premiumExpiration = Date.now() + ********;
    await user0.save();

    res = await request(app)
      .get('/v1/profile-view')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user.interestNames).to.be.undefined;
    expect(res.body.views[0].user.tags).to.be.undefined;

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/profile-view/profiles-i-viewed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.views.length).to.equal(1);
    expect(res.body.views[0].user.interestNames).to.be.undefined;
    expect(res.body.views[0].user.tags).to.be.undefined;
  });
});

