const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now, index: true },
  user: { type: String, ref: 'User' },
  retryCount: { type: Number, default: 0 },
  receipt: { type: String },
  error: { type: String },
});

const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('IapValidationFailure', schema);
