const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  user: { type: String, index: true },
  sessionId: { type: String, unique: true, required: true },

  status: {
    type: String,
    enum: ['CREATED','IN_PROGRESS','SUCCEEDED','FAILED','EXPIRED'],
    default: 'CREATED'
  },

  confidence: Number,
  decision: { type: String, enum: ['pass','fail','unknown'], default: 'unknown' },

  referenceImage: String,
  auditImages: [String],

  // housekeeping
  createdByToken: String,   // idempotency token used for CreateFaceLivenessSession
  expiresAt: Date,          // session TTL (Rekognition session validity window)
}, { timestamps: true });

// Export schema =====================================================================================================================================================================
let conn = connectionLib.getRecordsConnection() || mongoose;
module.exports = conn.model('AWSLivenessVerification', schema);
