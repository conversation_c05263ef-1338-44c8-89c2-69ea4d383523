const aws = require('aws-sdk');
const s3 = require('./s3');
const { cloudwatch } = require('./cloudwatch');
const ImageModeration = require('../models/image-moderation');
const constants = require('../lib/constants');

const rekognition = new aws.Rekognition({
  accessKeyId: process.env.AWS_KEY,
  secretAccessKey: process.env.AWS_SECRET,
  region: 'us-east-1',
});

const BANNED_COLL_ID = `BannedUsers_${process.env.NODE_ENV}`;
const UPDATED_BANNED_USERS_COLL_ID = `UpdatedBannedUsers_${process.env.NODE_ENV}`;

async function moderatePicture(key, isVideoThumbnail) {
  const params = {
    Image: {
      S3Object: {
        Bucket: s3.AWS_S3_BUCKET,
        Name: key,
      },
    },
    MinConfidence: '75',
  };
  try {
    const data = await rekognition.detectModerationLabels(params).promise();
    console.log(data);
    let moderationLabel;
    for (const label of data.ModerationLabels) {
      if (isVideoThumbnail) {
        if (
             label.Name == 'Exposed Male Genitalia' && label.Confidence >= 96.6
          || label.Name == 'Exposed Female Nipple' && label.Confidence >= 96.7
          || label.Name == 'Exposed Buttocks or Anus' && label.Confidence >= 91
        ) {
          moderationLabel = label;
          break;
        }
      } else {
        if (
             label.Name == 'Explicit Nudity' && label.Confidence >= 91.8
          || label.Name == 'Exposed Male Genitalia' && label.Confidence >= 91.25
          || label.Name == 'Exposed Female Genitalia' && label.Confidence >= 91.25
        ) {
          moderationLabel = label;
          break;
        }
      }
    }
    await ImageModeration.create({
      key: key,
      url: `${constants.IMAGE_DOMAIN}${key}`,
      moderationLabels: data.ModerationLabels,
      isVideoThumbnail,
      isFlagged: moderationLabel !== undefined,
      flaggedModerationLabel: moderationLabel,
    });
    return moderationLabel;
  } catch (err) {
    console.log('ThirdPartyError Rekognition:', err);
    await ImageModeration.create({
      key: key,
      url: `${constants.IMAGE_DOMAIN}${key}`,
      isError: true,
      errorMessage: err.message,
    });
    const params = {
      MetricData: [
        {
          MetricName: 'RekognitionErrors',
          Value: 1,
        },
      ],
      Namespace: `ThirdPartyMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();

    // let the picture through on rekognition error
  }
}

async function containsFace(key) {

  const params = {
    Image: {
      S3Object: {
        Bucket: s3.AWS_S3_BUCKET,
        Name: key
      },
    }
  };
  try {
    const data= await rekognition.detectFaces(params).promise();
    console.log(data);

    for (let face of data.FaceDetails) {
      if (face.Confidence >= 99) {
        return true;
      }
    }
  } catch (err) {
    console.log('Err');
    console.log('ThirdPartyError Rekognition-detectFaces:', err);
    const params = {
      MetricData: [
        {
          MetricName: 'Rekognition-detectFace-Errors',
          Value: 1,
        },
      ],
      Namespace: `ThirdPartyMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }
}

/*

Example Res for FaceDetect:

{"FaceDetails":[{"BoundingBox":{"Width":0.023449202999472618,"Height":0.01772645302116871,"Left":0.5025492906570435,"Top":0.5655564665794373},"Landmarks":[{"Type":"eyeLeft","X":0.5069561004638672,"Y":0.5729728937149048},{"Type":"eyeRight","X":0.5164220929145813,"Y":0.5718976259231567},{"Type":"mouthLeft","X":0.5088135600090027,"Y":0.5815503001213074},{"Type":"mouthRight","X":0.5167157053947449,"Y":0.5806710124015808},{"Type":"nose","X":0.5095794200897217,"Y":0.5769956111907959}],"Pose":{"Roll":-13.363875389099121,"Yaw":-9.375675201416016,"Pitch":2.553509473800659},"Quality":{"Brightness":48.699790954589844,"Sharpness":1.399810552597046},"Confidence":67.41455841064453}]}
{"FaceDetails":[{"BoundingBox":{"Width":0.8955389857292175,"Height":0.5613205432891846,"Left":0.0499160960316658,"Top":0.30683207511901855},"Landmarks":[{"Type":"eyeLeft","X":0.28982317447662354,"Y":0.5312063097953796},{"Type":"eyeRight","X":0.6801839470863342,"Y":0.4970347285270691},{"Type":"mouthLeft","X":0.3586726784706116,"Y":0.8269520998001099},{"Type":"mouthRight","X":0.6855742335319519,"Y":0.797565758228302},{"Type":"nose","X":0.5232535004615784,"Y":0.6809647083282471}],"Pose":{"Roll":-6.068292140960693,"Yaw":-0.767395555973053,"Pitch":0.5489208698272705},"Quality":{"Brightness":68.78474426269531,"Sharpness":95.51618957519531},"Confidence":99.96833801269531}]}
{"FaceDetails":[]}

*/

async function addBannedFaceToIndex(key, userId, collectionId = BANNED_COLL_ID) {
  const Image = {
    S3Object: {
      Bucket: s3.AWS_S3_BUCKET,
      Name: key,
    },
  };
  try {
    const data = await rekognition.indexFaces({
      CollectionId: collectionId, // By default add to previous banned users collection
      ExternalImageId: userId,
      Image,
      MaxFaces: 1, // to not index faces if there are more than one face in the image
    }).promise();
    console.log(`Rekognition:IndexFaces Res- ${JSON.stringify(data)}`);
    return data.FaceRecords?.[0]?.Face?.FaceId;
  } catch (err) {
    console.log('ThirdPartyError Rekognition-IndexFaces:', err);
    const params = {
      MetricData: [
        {
          MetricName: 'RekognitionErrors-IndexFace',
          Value: 1,
        },
      ],
      Namespace: `ThirdPartyMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }
}

/*
Example Res for IndexFaces:
{ "FaceRecords": [{ "Face": { "FaceId": "4133a355-f3db-46ba-ab45-acc08f4f4613", "BoundingBox": { "Width": 0.5433781743049622, "Height": 0.5498051643371582, "Left": 0.22271251678466797, "Top": 0.07872041314840317 }, "ImageId": "9789fe7d-e89b-3687-871d-2c6b77fd410e", "Confidence": 99.9986801147461 }, "FaceDetail": { "BoundingBox": { "Width": 0.5433781743049622, "Height": 0.5498051643371582, "Left": 0.22271251678466797, "Top": 0.07872041314840317 }, "Landmarks": [{ "Type": "eyeLeft", "X": 0.35527119040489197, "Y": 0.30010828375816345 }, { "Type": "eyeRight", "X": 0.595363438129425, "Y": 0.2952188551425934 }, { "Type": "mouthLeft", "X": 0.38605326414108276, "Y": 0.47353896498680115 }, { "Type": "mouthRight", "X": 0.5860294103622437, "Y": 0.46966075897216797 }, { "Type": "nose", "X": 0.4672318398952484, "Y": 0.3845202624797821 }], "Pose": { "Roll": -3.535372018814087, "Yaw": -4.122146129608154, "Pitch": 7.786780834197998 }, "Quality": { "Brightness": 66.14601135253906, "Sharpness": 97.45164489746094 }, "Confidence": 99.9986801147461 } }], "FaceModelVersion": "6.0", "UnindexedFaces": [] }
*/

async function findBannedFace(key, collectionId = UPDATED_BANNED_USERS_COLL_ID) {
  if (process.env.NODE_ENV == 'beta') {
    // disable on beta to avoid issues during beta testing
    return;
  }

  const Image = {
    S3Object: {
      Bucket: s3.AWS_S3_BUCKET,
      Name: key,
    },
  };
  try {
    const data = await rekognition.searchFacesByImage({
      CollectionId: collectionId, // By default search in new banned users collection
      Image,
      MaxFaces: 1,
      FaceMatchThreshold: 99,
    }).promise();
    console.log(`Rekognition:searchFacesByImage Res- ${JSON.stringify(data)}`);
    if (data.SearchedFaceConfidence < 99) {
      return;
    }
    if (data.FaceMatches.length > 0) {
      return data.FaceMatches[0];
    }
  } catch (err) {
    if (err.message.includes('There are no faces in the image.')) {
      return;
    }
    console.log('ThirdPartyError Rekognition-searchFacesByImage:', err);
    const params = {
      MetricData: [
        {
          MetricName: 'RekognitionErrors-searchFacesByImage',
          Value: 1,
        },
      ],
      Namespace: `ThirdPartyMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }
}

/*
Example Res for SearchFacesByImage:

//Match Found
{"SearchedFaceBoundingBox":{"Width":0.6055485010147095,"Height":0.426268607378006,"Left":0.28473424911499023,"Top":0.15933704376220703},"SearchedFaceConfidence":99.99935913085938,"FaceMatches":[{"Similarity":99.99906158447266,"Face":{"FaceId":"4133a355-f3db-46ba-ab45-acc08f4f4613","BoundingBox":{"Width":0.5433779954910278,"Height":0.5498049855232239,"Left":0.22271299362182617,"Top":0.07872039824724197},"ImageId":"9789fe7d-e89b-3687-871d-2c6b77fd410e","Confidence":99.99870300292969}}],"FaceModelVersion":"6.0"}

//Match Not Found
{"SearchedFaceBoundingBox":{"Width":0.15600109100341797,"Height":0.3024405241012573,"Left":0.43167370557785034,"Top":0.0507936030626297},"SearchedFaceConfidence":99.99811553955078,"FaceMatches":[],"FaceModelVersion":"6.0"}

*/

async function compareFaces(sourceKey, targetKey) {
  try {
    const params = {
      SourceImage: {
        S3Object: {
          Bucket: s3.AWS_S3_BUCKET,
          Name: sourceKey,
        },
      },
      TargetImage: {
        S3Object: {
          Bucket: s3.AWS_S3_BUCKET,
          Name: targetKey,
        },
      },
      SimilarityThreshold: 80
    }
    const data = await rekognition.compareFaces(params).promise();
    console.log(data);

    return data;
  } catch (err) {
    if (err.code == 'InvalidParameterException') {
      // this error means no face detected
    } else {
      console.log(err);
    }
    return {};
  }
}

async function containsText(key) {

  const params = {
    Image: {
      S3Object: {
        Bucket: s3.AWS_S3_BUCKET,
        Name: key
      },
    }
  };
  try {
    const data = await rekognition.detectText(params).promise();
    console.log(data);

    if (data.TextDetections.length > 0) {
      return true;
    }
  } catch (err) {
    console.log(err);
  }
  return false;
}

async function deleteFacesById(faceIds, collectionId = BANNED_COLL_ID) {
  const params = {
    CollectionId: collectionId,
    FaceIds: faceIds,
  };
  try {
    const data = await rekognition.deleteFaces(params).promise();
    if (data?.DeletedFaces?.length !== faceIds.length) {
      console.log(`Failed to delete faceIDs from rekognition: ${faceIds.filter((id) => !data?.DeletedFaces?.includes(id))}`);
    }
  } catch (err) {
    console.log(`Error deleting face ids from rekognition: ${err}`);
  }
}

async function detectLabels(key) {
  const params = {
    Image: {
      S3Object: {
        Bucket: s3.AWS_S3_BUCKET,
        Name: key
      },
    }
  };
  try {
    const data = await rekognition.detectLabels(params).promise();
    console.log(data);
  } catch (err) {
    console.log(err);
  }
}

async function createFaceLivenessSession(userId) {
  const params = {
    Settings: {
      AuditImagesLimit: 4,
      OutputConfig: {
        S3Bucket: process.env.AWS_S3_BUCKET,
        S3KeyPrefix: `${userId}/verification/`,
      },
    },
  };
  const { SessionId } = await rekognition.createFaceLivenessSession(params).promise();
  return { sessionId: SessionId };
}

async function getFaceLivenessSessionResults(sessionId) {
  const { Confidence, Status, ReferenceImage, AuditImages } =
    await rekognition.getFaceLivenessSessionResults({ SessionId: sessionId }).promise();
  return { Confidence, Status, ReferenceImage, AuditImages };
}

module.exports = {
  rekognition,
  moderatePicture,
  containsFace,
  addBannedFaceToIndex,
  findBannedFace,
  compareFaces,
  containsText,
  UPDATED_BANNED_USERS_COLL_ID,
  BANNED_COLL_ID,
  deleteFacesById,
  detectLabels,
  createFaceLivenessSession,
  getFaceLivenessSessionResults,
};
