const mongoose = require('mongoose');
const iap = require('in-app-purchase');
const moment = require('moment');
const ct = require('countries-and-timezones');
const { stringify } = require('flatted');
const { serializeError } = require('serialize-error');
const { DateTime } = require('luxon');
const httpErrors = require('./http-errors');
const PurchaseReceipt = require('../models/purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt');
const BoostTransaction = require('../models/boost-transaction');
const SuperLikeTransaction = require('../models/super-like-transaction');
const StripeReceipt = require('../models/stripe-receipt');
const IapValidationFailure = require('../models/iap-validation-failure');
const UserMetadata = require('../models/user-metadata');
const Chat = require('../models/chat');
const Action = require('../models/action');
const coinsLib = require('./coins');
const profilesLib = require('../lib/profiles-v3');
const { get1MonthFullPrice } = require('./pricing');
const { getFullCountryName } = require('./location');
const StickerPackPurchaseReceipt = require('../models/sticker-pack-purchase-receipt');
const StickerPack = require('../models/sticker-pack');
const { getExchangeData } = require('./currency-exchange');
const WebVisitor = require('../models/web-visitor');
const { googlePlayEmitter } = require('./google-play');
const { conversionEmitter } = require('./../lib/conversion');
const { conversionEvent } = require('./constants');
const appStoreConnectLib = require('../lib/app-store-connect');
const reportLib = require('../lib/report');
const socketLib = require('../lib/socket');

iap.config({
  /* Configurations for Apple */
  appleExcludeOldTransactions: true, // if you want to exclude old transaction, set this to true. Default is false
  applePassword: process.env.IAP_APPLE_PASSWORD, // this comes from iTunes Connect (You need this to valiate subscriptions)

  /* Configurations for Google Play */
  googlePublicKeyStrSandBox: process.env.IAP_GOOGLE_PUBLIC_KEY, // this is the google iap-sandbox public key string
  googlePublicKeyStrLive: process.env.IAP_GOOGLE_PUBLIC_KEY, // this is the google iap-live public key string
  googleAccToken: process.env.IAP_GOOGLE_ACC_TOKEN, // optional, for Google Play subscriptions
  googleRefToken: process.env.IAP_GOOGLE_REF_TOKEN, // optional, for Google Play subscritions
  googleClientID: process.env.IAP_GOOGLE_CLIENT_ID, // optional, for Google Play subscriptions
  googleClientSecret: process.env.IAP_GOOGLE_CLIENT_SECRET, // optional, for Google Play subscriptions

  /* Configurations all platforms */
  verbose: false, // Output debug logs to stdout stream
});

iap.setup()
  .then(() => {
    console.log('Finished setting up in-app-purchase library.');
  })
  .catch((error) => {
    console.log(`Error setting up in-app-purchase library. Shutting down server. ${error}`);
    process.exit(-1);
  });

function getGoogleRevenue({ priceCurrencyCode, priceAmountMicros }) {
  const exchangeData=getExchangeData();
  if (priceCurrencyCode && priceAmountMicros) {
    if (exchangeData[priceCurrencyCode]) {
      return exchangeData[priceCurrencyCode] * priceAmountMicros / 1000000;
    }
  }
}

function getCountryCode(user, receipt) {
  let countryCode;

  // if google, receipt will contain country code
  if (receipt) {
    countryCode = receipt.countryCode || receipt.regionCode;
    if (countryCode) {
      let countryName = getFullCountryName(countryCode);
      user.googlePlayCountry = countryName;
      if (user.signupCountry && user.signupCountry != user.googlePlayCountry) {
        user.signupCountryIncorrect = true;
      }
    }
  }

  // backup methods of checking country
  if (!countryCode) {
    countryCode = user.actualCountryCode;
  }
  if (!countryCode && user.timezone) {
    const country = ct.getCountryForTimezone(user.timezone);
    if (country) {
      countryCode = country.id;
    }
  }
  return countryCode;
}

function getDiscountFromProductId(productId) {
  let discount = 1;
  if (productId.includes('discount')) {
    const match = productId.match(/discount_(\d+)/);
    discount = (1 - parseInt(match[1]) / 100) || 1;
  } else if (productId.includes('_d')) {
    const match = productId.match(/_d(\d+)/);
    if (match) {
      discount = (1 - parseInt(match[1]) / 100) || 1;
    }
  }
  if (productId.includes('free_trial')) { discount = 0; }
  return discount;
}

async function processPurchaseData(user, purchaseData, purchasedFrom, price, currency) {
  if (purchaseData.length == 0) {
    console.log(`User ${user._id} receipt is expired`);
    return;
  }

  let newestReceipt = null;
  let expirationDate = null;
  for (const data of purchaseData) {
    if (data.productId.includes('lifetime')) {
      newestReceipt = data;
      expirationDate = moment().add(100, 'years');
      break;
    }
    const thisExpirationDate = new Date(Number(data.expirationDate));
    if (thisExpirationDate < new Date()) {
      continue;
    }
    if (!expirationDate || thisExpirationDate > expirationDate) {
      newestReceipt = data;
      expirationDate = thisExpirationDate;
    }
  }

  if (!expirationDate) {
    console.log(`User ${user._id} receipt is expired`);
    return;
  }

  const GOOGLE = 'google';
  const service = newestReceipt.service == GOOGLE ? GOOGLE : 'apple';
  const isGoogle = service == GOOGLE;

  const subscriptionId = isGoogle ? newestReceipt.purchaseToken : newestReceipt.originalTransactionId;
  const transactionId = isGoogle ? newestReceipt.orderId : newestReceipt.transactionId;
  const { productId } = newestReceipt;

  const isInfinity = productId.toLowerCase().includes('infinity') && !productId.includes('v5');
  const isInfinityV2 = productId.includes('infinity') && productId.includes('v5');
  const isGodMode = productId.includes('premium') || productId == 'lifetime_v5';
  const isUnlimitedLikes = productId.includes('unlimited_likes');
  const isUnlimitedDms = productId.includes('unlimited_dms');
  if (!isInfinity && !isInfinityV2 && !isGodMode && !isUnlimitedLikes && !isUnlimitedDms) {
    console.log('invalid product');
    return;
  }

  let previousPurchase = await PurchaseReceipt.findOne({
    subscriptionId,
    transactionId,
    productId,
  });
  if (previousPurchase) {
    if (purchasedFrom) {
      previousPurchase.purchasedFrom = purchasedFrom;
      await previousPurchase.save();
      if (!user.metrics.purchasedPremiumFrom) {
        user.metrics.purchasedPremiumFrom = purchasedFrom;
        await user.save();
      }
    }
    console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    return;
  }

  let expirationField;
  if (isInfinity) { expirationField = 'premiumExpiration'; }
  if (isInfinityV2) { expirationField = 'premiumV2Expiration'; }
  if (isGodMode) { expirationField = 'godModeExpiration'; }
  if (isUnlimitedLikes) { expirationField = 'unlimitedLikesExpiration'; }
  if (isUnlimitedDms) { expirationField = 'unlimitedDmsExpiration'; }
  const previousExpirationDate = user[expirationField];

  if (previousExpirationDate && expirationDate <= previousExpirationDate) {
    console.log(`User ${user._id} premium subscription still active. Saved expiration date: ${previousExpirationDate}, receipt expiration date: ${expirationDate}`);
    return;
  }

  let previousPurchases = await PurchaseReceipt.find({
    subscriptionId,
    user: user._id,
    productId: newestReceipt.productId,
  });

  const renewalNumber = previousPurchases.length;
  const promoCode = renewalNumber ? previousPurchases[0].promoCode
    : user.savedPromoCode;

  let months = 0;
  if (productId.includes('month')) {
    const match = productId.match(/(\d+)_month/);
    months = parseInt(match[1]) || 0;
  } else if (productId.includes('_m')) {
    const match = productId.match(/_m(\d+)/);
    if (match) {
      months = parseInt(match[1]) || 0;
    }
  }
  if (productId.includes('week')) {
    const match = productId.match(/boo_infinity_(\d+)_week/);
    if (match) {
      months = (parseInt(match[1]) || 0) / 4;
    }
  } else if (productId.includes('_w')) {
    const match = productId.match(/_w(\d+)/);
    if (match) {
      months = (parseInt(match[1]) || 0) / 4;
    }
  }

  let discount = getDiscountFromProductId(productId);
  if (renewalNumber > 0) { discount = 1; }

  let bonusCoins = 0;
  if (isInfinity) {
    bonusCoins = user.versionAtLeast('1.11.53') ? 1000 : months * 1000;
  }
  if (user.versionAtLeast('1.11.58')) {
    bonusCoins = 0;
  }

  let variantMultiplier = 1;
  if (productId.includes('variant') || productId.includes('_x1')) { variantMultiplier = 1.5; }
  if (productId.includes('variant_1') || productId.includes('_x2')) { variantMultiplier = 0.5; }

  let productMultiplier = 1;
  if (isInfinity) { productMultiplier = 1; }
  if (isUnlimitedLikes) { productMultiplier = 0.75; }
  if (isUnlimitedDms) { productMultiplier = 0.75; }

  let countryCode = getCountryCode(user, newestReceipt);
  let countryName = getFullCountryName(countryCode);

  // get google price from receipt
  let googlePrice;
  if (isGoogle) {
    googlePrice = getGoogleRevenue(newestReceipt);

    if (productId.includes('infinity_lifetime')) {
      // lifetime receipts do not contain currency data
      // but we know that lifetime is 10x the price of 1 month
      const match = productId.match(/infinity_lifetime(.+)/);
      const version = match ? match[1] : '';
      const oneMonthProductId = version ? `infinity_m1${version}` : 'boo_infinity_1_month';
      const receipt = await PurchaseReceipt.findOne({
        productId: oneMonthProductId,
        service: 'google',
        'fullReceipt.countryCode': newestReceipt.regionCode,
      })
        .sort('-purchaseDate');
      if (receipt) {
        googlePrice = 10 * receipt.revenue;
      } else {
        const threeMonthProductId = version ? `infinity_m3${version}_d50` : 'boo_infinity_3_months_discount_50';
        const receipt = await PurchaseReceipt.findOne({
          productId: threeMonthProductId,
          service: 'google',
          'fullReceipt.countryCode': newestReceipt.regionCode,
        })
          .sort('-purchaseDate');
        if (receipt) {
          googlePrice = 10 * receipt.revenue;
        }
      }
    }
  }

  // for apple, use app store connect api to fetch the price and country
  if (service == 'apple') {
    const transaction = await appStoreConnectLib.getTransaction(subscriptionId, transactionId);
    console.log(`appStoreConnectLib getTransaction: subscriptionId: ${subscriptionId}, transactionId: ${transactionId}, transaction: ${transaction ? JSON.stringify(transaction) : null}`);
    if (transaction) {
      if (transaction.currency && (price != transaction.price || currency != transaction.currency)) {
        console.log(`appStoreConnectLib using price: subscriptionId: ${subscriptionId}, transactionId: ${transactionId}, price: ${price} ${currency} -> ${transaction.price} ${transaction.currency}`);
        price = transaction.price;
        currency = transaction.currency;
      }
      if (transaction.storefront) {
        const countryNameFromAppStore = getFullCountryName(transaction.storefront);
        if (countryName != countryNameFromAppStore) {
          console.log(`appStoreConnectLib using country: ${countryName} -> ${countryNameFromAppStore}`);
          countryName = countryNameFromAppStore;
        }
      }
    }
  }


  let revenue;

  // calculate price
  if (googlePrice) {
    revenue = googlePrice;
    revenue *= discount;
  } else if (price && currency && getExchangeData()[currency]) {
    revenue = getExchangeData()[currency] * price;
  } else {
    revenue = get1MonthFullPrice(countryCode);
    if (months == 0.25) { revenue *= 0.5; }
    if (months == 3) { revenue *= 2; }
    if (months == 6) {
      if (productId.includes('v2')) {
        revenue *= 4;
      } else {
        revenue *= 3.5;
      }
    }
    if (months == 12) { revenue *= 6.5; }
    if (productId.includes('infinity_lifetime')) { revenue *= 10; }
    revenue *= variantMultiplier;
    revenue *= productMultiplier;
    revenue *= discount;
  }
  const daysOnPlatformBeforePurchase = Math.abs(moment().diff(user.createdAt, 'days'));

  // for apple, check bundleId
  let isFraudulent;
  if (service == 'apple' && newestReceipt.bundleId && newestReceipt.bundleId != 'enterprises.dating.boo') {
    isFraudulent = true;
    revenue = 0;
  }

  const purchaseReceipt = new PurchaseReceipt({
    user: user._id,
    promoCode: promoCode || undefined,
    service,
    productId: newestReceipt.productId,
    purchaseDate: isGoogle ? newestReceipt.purchaseDate : newestReceipt.purchaseDateMs,
    expirationDate: newestReceipt.expirationDate,
    transactionId,
    subscriptionId,
    originalPurchaseDate: isGoogle ? newestReceipt.startTimeMillis : newestReceipt.originalPurchaseDateMs,
    fullReceipt: newestReceipt,
    daysOnPlatformBeforePurchase,
    numberOfMatchesBeforePurchase: user.metrics.numMatches,
    numberOfLikesSentBeforePurchase: user.metrics.numLikesSent,
    renewalNumber,
    country: countryName,
    timezone: user.timezone,
    kochava: user.kochava,
    kochavaNetwork: user.kochava?.network,
    appsflyer: user.appsflyer || undefined,
    utm_source: user.utm_source,
    utm_medium: user.utm_medium,
    utm_campaign: user.utm_campaign,
    utm_content: user.utm_content,
    adset_name: user.adset_name,
    revenue,
    purchasedFrom,
    userMetrics: user.metrics,
    userEvents: user.events,
    isFraudulent,
  });
  if (price) {
    purchaseReceipt.price = price;
  } else if (newestReceipt.priceAmountMicros) {
    purchaseReceipt.price = newestReceipt.priceAmountMicros / 1000000;
  }
  if (currency) {
    purchaseReceipt.currency = currency;
  } else if (newestReceipt.priceCurrencyCode) {
    purchaseReceipt.currency = newestReceipt.priceCurrencyCode;
  }
  if (discount > 0 && discount < 1) {
    purchaseReceipt.saleReason = user.premiumFlashSaleReason;
  }
  const savedPurchaseReceipt = await purchaseReceipt.save().catch((err) => {
    // return errors except for duplicate key (duplicate receipt)
    if (err.code == 11000) {
      console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used (detected during save)`);
    } else {
      throw err;
    }
  });
  if (!savedPurchaseReceipt) {
    return;
  }

  if (productId.includes('free_trial') && renewalNumber == 0) {
    user.metrics.gotFreeTrial = true;
  } else {
    user.metrics.madePurchase = true;
    user.metrics.numPurchases += 1;
    user.currentDayMetrics.numPurchases += 1;
  }

  if (renewalNumber > 0) {
    user.metrics.numRenewals += 1;
  }

  user.productIdPurchased = productId;
  user.metrics.revenue += revenue;
  user.currentDayMetrics.revenue += revenue;

  await WebVisitor.updateOne(
    { user: user._id },
    {
      $inc: { revenue: revenue },
      $set: { madePurchase: 1, purchasedInfinity: 1 },
    },
  );

  if (user.metrics.daysOnPlatformBeforePurchase === undefined) {
    user.metrics.daysOnPlatformBeforePurchase = Math.abs(moment().diff(user.createdAt, 'days'));
  }

  if (discount > 0 && discount < 1) {
    user.metrics.numSalePurchases += 1;
    user.metrics.saleRevenue += revenue;
  }

  console.log(`User ${user._id} purchased premium subscription. Previous expiration date: ${previousExpirationDate}. New expiration date: ${expirationDate}, transaction id ${transactionId}, productId: ${productId}, months: ${months}, discount: ${discount}, variantMultiplier: ${variantMultiplier}, revenue: ${revenue}, bonusCoins: ${bonusCoins}, totalNumPurchases: ${user.metrics.numPurchases}, totalRevenue: ${user.metrics.revenue}`);

  if (isGodMode) {
    user.godModeMonthlySuperLikeAnniversary = new Date();
    user.numSuperLikes += 4;
  }
  if (isInfinity) {
    await user.activateInfinitySuperLikes();
  }

  user.purchases.push({
    productType: 'infinity',
    productId,
    daysOnPlatformBeforePurchase,
    revenue,
  });

  user[expirationField] = expirationDate;
  user.savedPromoCode = undefined;
  if (purchasedFrom) {
    user.metrics.purchasedPremiumFrom = purchasedFrom;
  }

  conversionEmitter.emit(conversionEvent.PURCHASE, {user, productId: purchaseReceipt.productId, qty: newestReceipt.quantity , transactionId: purchaseReceipt.transactionId, currency: purchaseReceipt.currency, price: purchaseReceipt.price})

  await user.save();

  if (bonusCoins) {
    const coins = await coinsLib.updateCoins(
      {
        user: user._id,
      },
      {
        $inc: { coins: bonusCoins },
      },
      'bonus coins from boo infinity',
    );
    if (coins) {
      console.log(`User ${user._id} awarded ${bonusCoins} coins. New total: ${coins}`);
    }
  }

  if (isFraudulent) {
    await reportLib.shadowBan(user, null, 'fraudulent purchase with invalid bundleId', newestReceipt.bundleId);
  }

  return savedPurchaseReceipt;
}

async function processCoinPurchase(user, purchaseData, purchasedFrom, price, currency) {
  if (purchaseData.length == 0) {
    console.log(`User ${user._id} receipt has no coin purchase data`);
    return;
  }

  let newestReceipt = null;
  for (const data of purchaseData) {
    const { productId } = data;
    if (productId.includes('coins')) {
      newestReceipt = data;
      break;
    }
  }

  if (!newestReceipt) {
    console.log(`User ${user._id} receipt has no coin purchase data`);
    return;
  }

  const GOOGLE = 'google';
  const service = newestReceipt.service == GOOGLE ? GOOGLE : 'apple';
  const isGoogle = service == GOOGLE;

  const { productId, packageName, purchaseToken } = newestReceipt;
  if (isGoogle && productId && packageName && purchaseToken) {
    googlePlayEmitter.emit('consume',{packageId: packageName,productId: productId, purchaseToken: purchaseToken})
  }

  const transactionId = isGoogle ? newestReceipt.orderId : newestReceipt.transactionId;

  const previousPurchases = await CoinPurchaseReceipt.find({
    transactionId,
    productId,
  });
  if (previousPurchases.length) {
    // duplicate receipt - just return success
    console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    return;
  }

  let countryCode = getCountryCode(user, newestReceipt);
  let countryName = getFullCountryName(countryCode);

  let discount = getDiscountFromProductId(productId);

  const googlePrice = isGoogle ? getGoogleRevenue(newestReceipt) : undefined;

  const match = productId.match(/(\d+)_coins/);
  const coinsPurchased = parseInt(match[1]);

  let revenue;
  if (googlePrice) {
    revenue = googlePrice;
    revenue *= discount;
  } else if (price && currency && getExchangeData()[currency]) {
    revenue = getExchangeData()[currency] * price;
  } else {
    revenue = (coinsPurchased == 100) ? 2
      : (coinsPurchased == 1000) ? 10
        : (coinsPurchased == 4000) ? 30
          : (coinsPurchased == 10000) ? 50
            : 0;
    const multiplier = get1MonthFullPrice(countryCode) / 20;
    revenue *= multiplier;
    revenue *= discount;
  }

  await activateCoinPurchase({
    user,
    service,
    productId: newestReceipt.productId,
    purchaseDate: isGoogle ? newestReceipt.purchaseDate : newestReceipt.purchaseDateMs,
    transactionId,
    fullReceipt: newestReceipt,
    price,
    currency,
    revenue,
    purchasedFrom,
  });
}

async function activateCoinPurchase(params) {
  const {
    user,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    price,
    currency,
    revenue,
    purchasedFrom,
  } = params;

  const daysOnPlatformBeforePurchase = Math.abs(moment().diff(user.createdAt, 'days'));

  const countryCode = getCountryCode(user, fullReceipt);
  const countryName = getFullCountryName(countryCode);

  const match = productId.match(/(\d+)_coins/);
  const coinsPurchased = parseInt(match[1]);

  const purchaseReceipt = new CoinPurchaseReceipt({
    user: user._id,
    service,
    productCategory: 'coins',
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    daysOnPlatformBeforePurchase,
    numberOfMatchesBeforePurchase: user.metrics.numMatches,
    numberOfLikesSentBeforePurchase: user.metrics.numLikesSent,
    country: countryName,
    timezone: user.timezone,
    kochava: user.kochava,
    kochavaNetwork: user.kochava?.network,
    appsflyer: user.appsflyer || undefined,
    utm_source: user.utm_source,
    utm_medium: user.utm_medium,
    utm_campaign: user.utm_campaign,
    utm_content: user.utm_content,
    adset_name: user.adset_name,
    revenue,
    purchasedFrom,
  });
  if (price) {
    purchaseReceipt.price = price;
  } else if (fullReceipt?.priceAmountMicros) {
    purchaseReceipt.price = fullReceipt.priceAmountMicros / 1000000;
  }
  if (currency) {
    purchaseReceipt.currency = currency;
  } else if (fullReceipt?.priceCurrencyCode) {
    purchaseReceipt.currency = fullReceipt.priceCurrencyCode;
  }
  const savedPurchaseReceipt = await purchaseReceipt.save().catch((err) => {
    // return errors except for duplicate key (duplicate receipt)
    if (err.code == 11000) {
      console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    } else {
      throw err;
    }
  });
  if (!savedPurchaseReceipt) {
    return;
  }

  user.purchases.push({
    productType: 'coins',
    productId,
    daysOnPlatformBeforePurchase,
    revenue,
  });

  user.metrics.revenue += revenue;
  user.metrics.coinRevenue += revenue;
  user.metrics.numCoinPurchases += 1;
  user.metrics.numCoinsPurchased += coinsPurchased;
  user.currentDayMetrics.revenue += revenue;
  if (purchasedFrom) {
    user.metrics.purchasedCoinsFrom = purchasedFrom;
  }
  await user.save();

  conversionEmitter.emit(conversionEvent.PURCHASE, {user, productId: purchaseReceipt.productId, transactionId: purchaseReceipt.transactionId, currency: purchaseReceipt.currency, price: purchaseReceipt.price})

  await WebVisitor.updateOne(
    { user: user._id },
    {
      $inc: { revenue: revenue },
      $set: { madePurchase: 1 },
    },
  );

  const coins = await coinsLib.updateCoins(
    { user: user._id },
    { $inc: { coins: coinsPurchased } },
    'purchased coins',
  );
  if (coins) {
    console.log(`User ${user._id} purchased ${coinsPurchased} coins. New total: ${coins}`);
  }
}

async function processSuperLikePurchase(user, purchaseData, price, currency) {
  if (purchaseData.length == 0) {
    console.log(`User ${user._id} receipt has no super like purchase data`);
    return;
  }

  let newestReceipt = null;
  for (const data of purchaseData) {
    const { productId } = data;
    if (productId.includes('super_love')) {
      newestReceipt = data;
      break;
    }
  }

  if (!newestReceipt) {
    console.log(`User ${user._id} receipt has no super like purchase data`);
    return;
  }

  const GOOGLE = 'google';
  const service = newestReceipt.service == GOOGLE ? GOOGLE : 'apple';
  const isGoogle = service == GOOGLE;

  const { productId, packageName, purchaseToken } = newestReceipt;
  if (isGoogle && productId && packageName && purchaseToken) {
    googlePlayEmitter.emit('consume',{packageId: packageName,productId: productId, purchaseToken: purchaseToken})
  }

  const transactionId = isGoogle ? newestReceipt.orderId : newestReceipt.transactionId;

  const previousPurchases = await SuperLikePurchaseReceipt.find({
    transactionId,
    productId,
  });
  if (previousPurchases.length) {
    // duplicate receipt - just return success
    console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    return;
  }

  let countryCode = getCountryCode(user, newestReceipt);
  let countryName = getFullCountryName(countryCode);

  let numPurchased = 0;
  const match = productId.match(/super_love_(\d+)_/);
  numPurchased = parseInt(match[1]) || 0;

  let discount = getDiscountFromProductId(productId);

  const googlePrice = isGoogle ? getGoogleRevenue(newestReceipt) : undefined;

  let revenue;
  if (googlePrice) {
    revenue = googlePrice;
    revenue *= discount;
  } else if (price && currency && getExchangeData()[currency]) {
    revenue = getExchangeData()[currency] * price;
  } else {
    revenue = numPurchased == 3 ? 10
      : numPurchased == 12 ? 30
        : numPurchased == 50 ? 75
          : 0;
    const multiplier = get1MonthFullPrice(countryCode) / 20;
    revenue *= multiplier;
    revenue *= discount;
  }

  await activateSuperLikePurchase({
    user,
    service,
    productId: newestReceipt.productId,
    purchaseDate: isGoogle ? newestReceipt.purchaseDate : newestReceipt.purchaseDateMs,
    transactionId,
    fullReceipt: newestReceipt,
    price,
    currency,
    revenue,
  });
}

async function activateSuperLikePurchase(params) {
  const {
    user,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    price,
    currency,
    revenue,
  } = params;

  const daysOnPlatformBeforePurchase = Math.abs(moment().diff(user.createdAt, 'days'));

  const countryCode = getCountryCode(user, fullReceipt);
  const countryName = getFullCountryName(countryCode);

  const match = productId.match(/super_love_(\d+)_/);
  const numPurchased = parseInt(match[1]) || 0;

  const purchaseReceipt = new SuperLikePurchaseReceipt({
    user: user._id,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    daysOnPlatformBeforePurchase,
    numberOfMatchesBeforePurchase: user.metrics.numMatches,
    numberOfLikesSentBeforePurchase: user.metrics.numLikesSent,
    country: countryName,
    timezone: user.timezone,
    kochava: user.kochava,
    kochavaNetwork: user.kochava?.network,
    appsflyer: user.appsflyer || undefined,
    utm_source: user.utm_source,
    utm_medium: user.utm_medium,
    utm_campaign: user.utm_campaign,
    utm_content: user.utm_content,
    adset_name: user.adset_name,
    revenue,
  });
  if (price) {
    purchaseReceipt.price = price;
  } else if (fullReceipt?.priceAmountMicros) {
    purchaseReceipt.price = fullReceipt.priceAmountMicros / 1000000;
  }
  if (currency) {
    purchaseReceipt.currency = currency;
  } else if (fullReceipt?.priceCurrencyCode) {
    purchaseReceipt.currency = fullReceipt.priceCurrencyCode;
  }
  const savedPurchaseReceipt = await purchaseReceipt.save().catch((err) => {
    // return errors except for duplicate key (duplicate receipt)
    if (err.code == 11000) {
      console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    } else {
      throw err;
    }
  });
  if (!savedPurchaseReceipt) {
    return;
  }

  user.purchases.push({
    productType: 'superLike',
    productId,
    daysOnPlatformBeforePurchase,
    revenue,
  });

  user.metrics.revenue += revenue;
  user.metrics.superLikeRevenue += revenue;
  user.metrics.numSuperLikePurchases += 1;
  user.metrics.numSuperLikesPurchased += numPurchased;
  user.currentDayMetrics.revenue += revenue;
  user.numSuperLikes += numPurchased;
  await user.save();

  await SuperLikeTransaction.create({
    user: user._id,
    freeSuperLoveTransactionAmount: 0,
    freeSuperLoveNewBalance: user.numSuperLikesFree,
    paidSuperLoveTransactionAmount: numPurchased,
    paidSuperLoveNewBalance: user.numSuperLikes,
    description: 'purchased paid super love',
  });

  conversionEmitter.emit(conversionEvent.PURCHASE, {user, productId: purchaseReceipt.productId, transactionId: purchaseReceipt.transactionId, currency: purchaseReceipt.currency, price: purchaseReceipt.price})

  await WebVisitor.updateOne(
    { user: user._id },
    {
      $inc: { revenue: revenue },
      $set: { madePurchase: 1 },
    },
  );
}

async function processNeuronPurchase(user, purchaseData, price, currency) {
  if (purchaseData.length == 0) {
    console.log(`User ${user._id} receipt has no neuron purchase data`);
    return;
  }

  let newestReceipt = null;
  for (const data of purchaseData) {
    const { productId } = data;
    if (productId.includes('neurons')) {
      newestReceipt = data;
      break;
    }
  }

  if (!newestReceipt) {
    console.log(`User ${user._id} receipt has no neuron purchase data`);
    return;
  }

  const GOOGLE = 'google';
  const service = newestReceipt.service == GOOGLE ? GOOGLE : 'apple';
  const isGoogle = service == GOOGLE;

  const { productId, packageName, purchaseToken } = newestReceipt;
  if (isGoogle && productId && packageName && purchaseToken) {
    googlePlayEmitter.emit('consume',{packageId: packageName,productId: productId, purchaseToken: purchaseToken})
  }

  const transactionId = isGoogle ? newestReceipt.orderId : newestReceipt.transactionId;

  const previousPurchases = await NeuronPurchaseReceipt.find({
    transactionId,
    productId,
  });
  if (previousPurchases.length) {
    // duplicate receipt - just return success
    console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    return;
  }

  let countryCode = getCountryCode(user, newestReceipt);
  let countryName = getFullCountryName(countryCode);

  let numPurchased = 0;
  const match = productId.match(/(\d+)_neurons/);
  numPurchased = parseInt(match[1]) || 0;

  let discount = getDiscountFromProductId(productId);

  const googlePrice = isGoogle ? getGoogleRevenue(newestReceipt) : undefined;

  let revenue;
  if (googlePrice) {
    revenue = googlePrice;
    revenue *= discount;
  } else if (price && currency && getExchangeData()[currency]) {
    revenue = getExchangeData()[currency] * price;
  }

  await activateNeuronPurchase({
    user,
    service,
    productId: newestReceipt.productId,
    purchaseDate: isGoogle ? newestReceipt.purchaseDate : newestReceipt.purchaseDateMs,
    transactionId,
    fullReceipt: newestReceipt,
    price,
    currency,
    revenue,
  });
}

async function activateNeuronPurchase(params) {
  const {
    user,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    price,
    currency,
    revenue,
  } = params;

  const daysOnPlatformBeforePurchase = Math.abs(moment().diff(user.createdAt, 'days'));

  const countryCode = getCountryCode(user, fullReceipt);
  const countryName = getFullCountryName(countryCode);

  const match = productId.match(/(\d+)_neurons/);
  const numPurchased = parseInt(match[1]) || 0;

  const purchaseReceipt = new NeuronPurchaseReceipt({
    user: user._id,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    daysOnPlatformBeforePurchase,
    numberOfMatchesBeforePurchase: user.metrics.numMatches,
    numberOfLikesSentBeforePurchase: user.metrics.numLikesSent,
    purchaseNumber: 1 + user.metrics.numNeuronPurchases,
    country: countryName,
    timezone: user.timezone,
    kochava: user.kochava,
    kochavaNetwork: user.kochava?.network,
    appsflyer: user.appsflyer || undefined,
    utm_source: user.utm_source,
    utm_medium: user.utm_medium,
    utm_campaign: user.utm_campaign,
    utm_content: user.utm_content,
    adset_name: user.adset_name,
    revenue,
  });
  if (price) {
    purchaseReceipt.price = price;
  } else if (fullReceipt?.priceAmountMicros) {
    purchaseReceipt.price = fullReceipt.priceAmountMicros / 1000000;
  }
  if (currency) {
    purchaseReceipt.currency = currency;
  } else if (fullReceipt?.priceCurrencyCode) {
    purchaseReceipt.currency = fullReceipt.priceCurrencyCode;
  }
  const savedPurchaseReceipt = await purchaseReceipt.save().catch((err) => {
    // return errors except for duplicate key (duplicate receipt)
    if (err.code == 11000) {
      console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    } else {
      throw err;
    }
  });
  if (!savedPurchaseReceipt) {
    return;
  }

  user.purchases.push({
    productType: 'neurons',
    productId,
    daysOnPlatformBeforePurchase,
    revenue,
  });

  user.metrics.revenue += revenue;
  user.metrics.neuronRevenue += revenue;
  user.metrics.numNeuronPurchases += 1;
  user.metrics.numNeuronsPurchased += numPurchased;
  user.numBooAINeurons += numPurchased;
  await user.save();

  conversionEmitter.emit(conversionEvent.PURCHASE, {user, productId: purchaseReceipt.productId, transactionId: purchaseReceipt.transactionId, currency: purchaseReceipt.currency, price: purchaseReceipt.price})

  await WebVisitor.updateOne(
    { user: user._id },
    {
      $inc: { revenue: revenue },
      $set: { madePurchase: 1 },
    },
  );
}

async function processBoostPurchase(user, purchaseData, price, currency) {
  if (purchaseData.length == 0) {
    console.log(`User ${user._id} receipt has no boosts purchase data`);
    return;
  }

  let newestReceipt = null;
  for (const data of purchaseData) {
    const { productId } = data;
    if (productId.includes('boost')) {
      newestReceipt = data;
      break;
    }
  }

  if (!newestReceipt) {
    console.log(`User ${user._id} receipt has no boosts purchase data`);
    return;
  }

  const GOOGLE = 'google';
  const service = newestReceipt.service == GOOGLE ? GOOGLE : 'apple';
  const isGoogle = service == GOOGLE;

  const { productId, packageName, purchaseToken } = newestReceipt;
  if (isGoogle && productId && packageName && purchaseToken) {
    googlePlayEmitter.emit('consume',{packageId: packageName,productId: productId, purchaseToken: purchaseToken})
  }

  const transactionId = isGoogle ? newestReceipt.orderId : newestReceipt.transactionId;

  const previousPurchases = await BoostPurchaseReceipt.find({
    transactionId,
    productId,
  });
  if (previousPurchases.length) {
    // duplicate receipt - just return success
    console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    return;
  }

  let countryCode = getCountryCode(user, newestReceipt);
  let countryName = getFullCountryName(countryCode);

  let numPurchased = 0;
  const parts = productId.split('_');
  numPurchased = parseInt(parts[1]) || 0;

  let discount = getDiscountFromProductId(productId);

  const googlePrice = isGoogle ? getGoogleRevenue(newestReceipt) : undefined;

  let revenue;
  if (googlePrice) {
    revenue = googlePrice;
    revenue *= discount;
  } else if (price && currency && getExchangeData()[currency]) {
    revenue = getExchangeData()[currency] * price;
  }

  await activateBoostPurchase({
    user,
    service,
    productId: newestReceipt.productId,
    purchaseDate: isGoogle ? newestReceipt.purchaseDate : newestReceipt.purchaseDateMs,
    transactionId,
    fullReceipt: newestReceipt,
    price,
    currency,
    revenue,
  });
}

async function activateBoostPurchase(params) {
  const {
    user,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    price,
    currency,
    revenue,
  } = params;

  const daysOnPlatformBeforePurchase = Math.abs(moment().diff(user.createdAt, 'days'));

  const countryCode = getCountryCode(user, fullReceipt);
  const countryName = getFullCountryName(countryCode);

  const parts = productId.split('_');
  const numPurchased = parseInt(parts[1]) || 0;

  const purchaseReceipt = new BoostPurchaseReceipt({
    user: user._id,
    service,
    productId,
    purchaseDate,
    transactionId,
    fullReceipt,
    daysOnPlatformBeforePurchase,
    numberOfMatchesBeforePurchase: user.metrics.numMatches,
    numberOfLikesSentBeforePurchase: user.metrics.numLikesSent,
    purchaseNumber: 1 + user.metrics.numBoostPurchases,
    country: countryName,
    timezone: user.timezone,
    kochava: user.kochava,
    kochavaNetwork: user.kochava?.network,
    appsflyer: user.appsflyer || undefined,
    utm_source: user.utm_source,
    utm_medium: user.utm_medium,
    utm_campaign: user.utm_campaign,
    utm_content: user.utm_content,
    adset_name: user.adset_name,
    revenue,
  });
  if (price) {
    purchaseReceipt.price = price;
  } else if (fullReceipt?.priceAmountMicros) {
    purchaseReceipt.price = fullReceipt.priceAmountMicros / 1000000;
  }
  if (currency) {
    purchaseReceipt.currency = currency;
  } else if (fullReceipt?.priceCurrencyCode) {
    purchaseReceipt.currency = fullReceipt.priceCurrencyCode;
  }

  const savedPurchaseReceipt = await purchaseReceipt.save().catch((err) => {
    // return errors except for duplicate key (duplicate receipt)
    if (err.code == 11000) {
      console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
    } else {
      throw err;
    }
  });
  if (!savedPurchaseReceipt) {
    return;
  }

  user.purchases.push({
    productType: 'boost',
    productId,
    daysOnPlatformBeforePurchase,
    revenue,
  });

  user.metrics.revenue += revenue;
  user.metrics.boostRevenue += revenue;
  user.metrics.numBoostPurchases += 1;
  user.metrics.numBoostsPurchased += numPurchased;
  user.numBoosts += numPurchased;
  await user.save();

  await BoostTransaction.create({
    user: user._id,
    transactionAmount: numPurchased,
    newBalance: user.numBoosts,
    description: 'purchased boost',
  });

  conversionEmitter.emit(conversionEvent.PURCHASE, {user, productId: purchaseReceipt.productId, transactionId: purchaseReceipt.transactionId, currency: purchaseReceipt.currency, price: purchaseReceipt.price})

  await WebVisitor.updateOne(
    { user: user._id },
    {
      $inc: { revenue: revenue },
      $set: { madePurchase: 1 },
    },
  );
}

async function processStickerPackPurchase(user, purchaseData) {
  if (purchaseData.length == 0) {
    console.log(`User ${user._id} receipt has no sticker pack purchase data`);
    return;
  }

  let newestReceipt = null;
  for (const data of purchaseData) {
    const { productId } = data;
    const stickerPack = await StickerPack.findOne({ productId, premium: true });
    if (stickerPack) {
      newestReceipt = data;
      break;
    }
  }

  if (!newestReceipt) {
    console.log(`User ${user._id} receipt has no sticker pack purchase data`);
    return;
  }

  const GOOGLE = 'google';
  const service = newestReceipt.service == GOOGLE ? GOOGLE : 'apple';
  const isGoogle = service == GOOGLE;

  const { productId } = newestReceipt;
  const transactionId = isGoogle ? newestReceipt.orderId : newestReceipt.transactionId;

  {
    const previousPurchases = await StickerPackPurchaseReceipt.findOne({
      transactionId,
      productId,
    });
    if (previousPurchases) {
      // duplicate receipt - just return success
      console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
      return;
    }
  }

  let countryCode = user.actualCountryCode;
  let timezoneCountry;
  if (!countryCode && user.timezone) {
    const country = ct.getCountryForTimezone(user.timezone);
    if (country) {
      countryCode = country.id;
      timezoneCountry = getFullCountryName(countryCode);
    }
  }

  const googlePrice = isGoogle ? getGoogleRevenue(newestReceipt) : undefined;

  let revenue = 1;
  if (googlePrice) {
    revenue = googlePrice;
  } else if (isGoogle) {
    const multiplier = get1MonthFullPrice(countryCode) / 20;
    revenue *= multiplier;
  }

  {
    const purchaseReceipt = new StickerPackPurchaseReceipt({
      user: user._id,
      service,
      productId,
      purchaseDate: isGoogle ? newestReceipt.purchaseDate : newestReceipt.purchaseDateMs,
      transactionId,
      fullReceipt: newestReceipt,
      daysOnPlatformBeforePurchase: Math.abs(moment().diff(user.createdAt, 'days')),
      numberOfMatchesBeforePurchase: user.metrics.numMatches,
      numberOfLikesSentBeforePurchase: user.metrics.numLikesSent,
      country: user.actualCountry || timezoneCountry,
      timezone: user.timezone,
      revenue,
    });
    const savedPurchaseReceipt = await purchaseReceipt.save().catch((err) => {
      // return errors except for duplicate key (duplicate receipt)
      if (err.code == 11000) {
        console.log(`User ${user._id}, receipt with transaction id ${transactionId} was already used`);
      } else {
        throw err;
      }
    });
    if (!savedPurchaseReceipt) {
      return;
    }
    conversionEmitter.emit(conversionEvent.PURCHASE, {user, productId: purchaseReceipt.productId, transactionId: purchaseReceipt.transactionId, currency: purchaseReceipt.currency, price: purchaseReceipt.price, qty: newestReceipt.quantity ,})
  }

  user.metrics.revenue += revenue;
  user.metrics.stickerPackRevenue += revenue;
  user.metrics.numStickerPackPurchases += 1;
  user.currentDayMetrics.revenue += revenue;

  if (user.stickerPackPurchases) {
    if (user.stickerPackPurchases.includes(productId)) {
      console.log(`User ${user._id} might have purchased ${productId} more than once and transactionId${transactionId} might need to be rolled back`);
    } else {
      user.stickerPackPurchases.push(productId);
    }
  }
  await user.save();
}

async function backfillKochavaNetwork() {
  const users = mongoose.connection.db.collection('users');
  const query = { 'kochava.network': { $exists: true } };
  const options = {};
  const cursor = users.find(query, options).project({_id: 1, kochava: 1});
  let i = 0;
  let bulk1 = PurchaseReceipt.collection.initializeUnorderedBulkOp();
  let bulk2 = CoinPurchaseReceipt.collection.initializeUnorderedBulkOp();
  let bulk3 = SuperLikePurchaseReceipt.collection.initializeUnorderedBulkOp();
  for await (const user of cursor) {
    bulk1.find({ user: user._id }).update({ $set: { kochavaNetwork: user.kochava.network } });
    bulk2.find({ user: user._id }).update({ $set: { kochavaNetwork: user.kochava.network } });
    bulk3.find({ user: user._id }).update({ $set: { kochavaNetwork: user.kochava.network } });
    i++;
    if (i % 100 == 0) {
      await bulk1.execute();
      await bulk2.execute();
      await bulk3.execute();
      console.log(i);
      bulk1 = PurchaseReceipt.collection.initializeUnorderedBulkOp();
      bulk2 = CoinPurchaseReceipt.collection.initializeUnorderedBulkOp();
      bulk3 = SuperLikePurchaseReceipt.collection.initializeUnorderedBulkOp();
    }
  }
  await bulk1.execute();
  await bulk2.execute();
  await bulk3.execute();
  console.log(i);
}

async function backfillKochava() {
  console.log('backfillKochava');
  const models = [PurchaseReceipt,CoinPurchaseReceipt,SuperLikePurchaseReceipt,NeuronPurchaseReceipt];
  const collections = ['purchasereceipts','coinpurchasereceipts','superlikepurchasereceipts','neuronpurchasereceipts'];
  for (let i = 0; i < 4; i++) {
    const model = models[i];
    const collection = collections[i];
    console.log('backfillKochava', model, collection);
    await model.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userObj',
        }
      },
      {
        $unwind: '$userObj',
      },
      {
        $project: {
          kochava: '$userObj.kochava',
        }
      },
      {
        $merge: {
          into: collection,
          whenMatched: 'merge',
          whenNotMatched: 'discard',
        }
      },
    ]);
  }
}

async function backfillUTM() {
  const users = mongoose.connection.db.collection('users');
  const query = { 'utm_campaign': { $exists: true } };
  const options = {};
  const cursor = users.find(query, options).project({_id: 1, utm_source: 1, utm_medium: 1, utm_campaign: 1});
  let i = 0;
  let bulk1 = PurchaseReceipt.collection.initializeUnorderedBulkOp();
  let bulk2 = CoinPurchaseReceipt.collection.initializeUnorderedBulkOp();
  let bulk3 = SuperLikePurchaseReceipt.collection.initializeUnorderedBulkOp();
  let bulk4 = StripeReceipt.collection.initializeUnorderedBulkOp();
  for await (const user of cursor) {
    bulk1.find({ user: user._id }).update({ $set: { utm_source: user.utm_source, utm_medium: user.utm_medium, utm_campaign: user.utm_campaign } });
    bulk2.find({ user: user._id }).update({ $set: { utm_source: user.utm_source, utm_medium: user.utm_medium, utm_campaign: user.utm_campaign } });
    bulk3.find({ user: user._id }).update({ $set: { utm_source: user.utm_source, utm_medium: user.utm_medium, utm_campaign: user.utm_campaign } });
    bulk4.find({ user: user._id }).update({ $set: { utm_source: user.utm_source, utm_medium: user.utm_medium, utm_campaign: user.utm_campaign } });
    i++;
    if (i % 100 == 0) {
      await bulk1.execute();
      await bulk2.execute();
      await bulk3.execute();
      await bulk4.execute();
      console.log(i);
      bulk1 = PurchaseReceipt.collection.initializeUnorderedBulkOp();
      bulk2 = CoinPurchaseReceipt.collection.initializeUnorderedBulkOp();
      bulk3 = SuperLikePurchaseReceipt.collection.initializeUnorderedBulkOp();
      bulk4 = StripeReceipt.collection.initializeUnorderedBulkOp();
    }
  }
  await bulk1.execute();
  await bulk2.execute();
  await bulk3.execute();
  await bulk4.execute();
  console.log(i);
}

function isTransientIAPError(error) {
  if (!error) return false;

  const msg = String(error.message || '').toLowerCase();
  const code = error.code || '';

  return (
    // Network-level or system-level errors
    ['ETIMEDOUT', 'ECONNRESET', 'EAI_AGAIN', 'ENOTFOUND'].includes(code) ||
    // Known Google API/backend or library-level transient markers
    msg.includes('internal_failure') ||
    msg.includes('timeout') ||
    msg.includes('socket') ||
    msg.includes('network') ||
    // Poorly structured 5xx messages from the library
    /\bstatus\s*[:=]?\s*5\d{2}\b/.test(msg) ||
    // Explicit HTTP statusCode if present
    (error.statusCode >= 500 && error.statusCode < 600)
  );
}

function wrapValidator(validator, data) {
  return new Promise((resolve, reject) => {
    validator.validate(data, (err, validatedData) => {
      if (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        error.validatedData = validatedData;
        return reject(error);
      }
      resolve(validatedData);
    });
  });
}

async function validateReceiptWithRetry(user, data, validator, isLegacyIAPValidator, maxRetries = 3) {
  const errorMsg = 'Purchase could not be validated.';
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await wrapValidator(validator, data);
    } catch (error) {
      const retryable = isTransientIAPError(error);
      const lastAttempt = attempt === maxRetries;

      const isHackedReceipt =
        (error.validatedData && error.validatedData.status === 2) || // Apple hacked
        error.message === 'Status:400' || // Google hacked
        error.message === 'Status:403'; // Google from other source

      if (isHackedReceipt) {
        console.log(`[IAP Validation] Hacked receipt detected for user ${user._id}: ${error.message}`);
        throw httpErrors.invalidInputError(errorMsg);
      }

      // Retry logic is only relevant for legacy IAP
      if (isLegacyIAPValidator) {
        if (isLegacyIAPValidator && (!retryable || lastAttempt)) {
          console.log(`[IAP Validation] Non-hacked validation failure for user ${user._id} after ${attempt} attempt(s): ${error.message}`);

          try {
            await IapValidationFailure.create({
              user: user._id,
              retryCount: attempt,
              receipt: JSON.stringify(data),
              error: stringify(serializeError(error)),
            });
            console.log(`[IAP Validation] Logged validation failure to DB for user ${user._id}`);
          } catch (dbError) {
            console.log(`[IAP Validation] Failed to log validation failure for user ${user._id}:`, dbError);
          }

          throw httpErrors.applicationError(errorMsg);
        }

        // Retry case
        console.log(`[IAP Validation] Retry attempt ${attempt + 1}/${maxRetries} for user ${user._id} due to transient error: ${error.message}`);
        const backoff = 100 * 2 ** attempt;
        await new Promise((resolve) => setTimeout(resolve, backoff));
      } else {
        // For App Store JWS (non-legacy), no retries
        console.log(`[IAP Validation] Non-legacy validator used — skipping retries for user ${user._id}: ${error.message}`);
        throw httpErrors.applicationError(errorMsg);
      }
    }
  }
}

module.exports = {
  iap,
  processPurchaseData,
  processCoinPurchase,
  processStickerPackPurchase,
  processSuperLikePurchase,
  processNeuronPurchase,
  getDiscountFromProductId,
  backfillKochavaNetwork,
  backfillKochava,
  backfillUTM,
  activateCoinPurchase,
  activateSuperLikePurchase,
  activateNeuronPurchase,
  processBoostPurchase,
  activateBoostPurchase,
  validateReceiptWithRetry,
};
