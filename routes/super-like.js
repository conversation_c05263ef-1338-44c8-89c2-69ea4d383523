const express = require('express');
const router = express.Router();
const socketLib = require('../lib/socket');
const asyncHandler = require('express-async-handler');
const { translate } = require('../lib/translate');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('../lib/http-errors');
const { iap, processSuperLikePurchase, validateReceiptWithRetry } = require('../lib/iap');
const appStoreJwsValidator = require('../lib/app-store-connect');
const UserMetadata = require('../models/user-metadata');

module.exports = function () {
  router.put('/purchase', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (!req.body.receipt && !req.body.transactionJws) {
      return next(invalidInputError(errorMsg));
    }

    const validator = req.body.transactionJws ? appStoreJwsValidator : iap;
    const data = req.body.transactionJws ? req.body.transactionJws : req.body.receipt;

    const validatedData = await validateReceiptWithRetry(user, data, validator, !req.body.transactionJws);
    const purchaseData = validator.getPurchaseData(validatedData);

    console.log(validatedData);
    console.log(purchaseData);

    await processSuperLikePurchase(user, purchaseData, req.body.price, req.body.currency)
      .then(() => res.json({}))
      .catch((err) => {
        console.log(err);
        return next(applicationError());
      });
  }));

  router.put('/rateApp', asyncHandler(async (req, res, next) => {
    return res.json({});
  }))

  router.put('/detailedReview', asyncHandler(async (req, res, next) => {
    return res.json({});
  }))


  return router;
};
