const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const httpErrors = require('../lib/http-errors');
const User = require('../models/user');
const Question = require('../models/question');
const QuestionViewData = require('../models/question-view-data');
const socketLib = require('../lib/socket');
const coinsLib = require('../lib/coins');
const coinsConstants = require('../lib/coins-constants');
const { translate } = require('../lib/translate');

async function incrementFields(questionId, user, fields) {
  const question = await Question.findById(questionId);
  if (!question) {
    return;
  }

  await QuestionViewData.updateOne(
    { question: questionId, user: user._id },
    { $inc: fields, $set: { updatedAt: Date.now() } },
    { upsert: true },
  );

  const update = { $inc: fields };

  if (
    user?._id === question?.createdBy &&
    !question?.ownerSharedLink &&
    fields.numShares
  ) {
    update.$set = { ownerSharedLink: true };
  }

  await Question.updateOne({ _id: questionId }, update);

  const hour = moment().diff(question.createdAt, 'hours');
  if (hour >= 0 && hour <= 23) {
    for (const [key, value] of Object.entries(fields)) {
      question.hourlyEngagement[hour][key] += value;
    }
    await question.save();
  }
}

module.exports = function () {

  router.put('/', asyncHandler(async (req, res, next) => {
    const user = req.user;
    let numPostShares = 0;
    for (const question of req.body.questions) {
      try {
        await incrementFields(question.questionId, user, question.fieldsToIncrement);
      } catch (err) {
        console.log(err);
      }
      if (question.fieldsToIncrement.numShares) {
        user.metrics.numPostShares += question.fieldsToIncrement.numShares;
        numPostShares += question.fieldsToIncrement.numShares;
      }
      if (question.fieldsToIncrement.numClicks) {
        user.metrics.numPostClicks += question.fieldsToIncrement.numClicks;
      }
      if (question.fieldsToIncrement.numSecondsReadingOnFeed) {
        user.metrics.numSecondsReadingOnFeed += question.fieldsToIncrement.numSecondsReadingOnFeed;
      }
      if (question.fieldsToIncrement.numSecondsReadingComments) {
        user.metrics.numSecondsReadingComments += question.fieldsToIncrement.numSecondsReadingComments;
      }
    }
    await user.save();

    if (numPostShares) {
      if (user.versionAtLeast('1.13.13')) {
        await user.resetCurrentDayMetricsIfNeeded();
        const dailyLimit = coinsConstants.getSharePostsDailyRewardLimit();
        if (user.currentDayMetrics.coinsFromSharingPosts < dailyLimit) {
          const numCoins = Math.min(numPostShares, dailyLimit - user.currentDayMetrics.coinsFromSharingPosts);
          user.currentDayMetrics.coinsFromSharingPosts += numCoins;
          await user.save();

          const coins = await coinsLib.updateCoins(
            {
              user: user._id,
            },
            {
              $inc: { coins: numCoins },
            },
            'Share Posts Reward',
          );
          if (coins) {
            const coinRewards = [{
              caption: translate('Share Posts Reward', user.locale),
              rewardAmount: numCoins,
              newTotal: coins,
            }];
            socketLib.sendCoinRewards(
              user._id,
              coinRewards,
            );
          }
        }
      } else {
        socketLib.sendCoinRewards(
          user._id,
          await coinsLib.onSharePost(user),
        );
      }
    }

    res.json({});
  }));

  return router;
};
