const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const httpErrors = require('../lib/http-errors');
const User = require('../models/user');
const crypto = require('crypto');
const AWSLivenessVerification = require('../models/aws-liveness-verification');
const rekognitionLib = require('../lib/rekognition');
const userLib = require('../lib/user');
const verificationLib = require('../lib/verification');
const { updateUserScore } = require('../lib/score');
const socketLib = require('../lib/socket');

const PASS_THRESHOLD = 80;
const REGION = 'us-east-1';

module.exports = function () {

  router.post('/session', asyncHandler(async (req, res, next) => {
    // todo: rate limit

    const { sessionId } = await rekognitionLib.createFaceLivenessSession(req.uid);

    // Per AWS docs a session must be used promptly; treat as ~3 minutes until it expires.
    const expiresAt = new Date(Date.now() + 3 * 60 * 1000);

    const doc = await AWSLivenessVerification.create({
      user: req.uid,
      sessionId,
      status: 'CREATED',
      createdByToken: crypto.randomUUID(),
      expiresAt,
    });

    return res.json({
      sessionId: doc.sessionId,
      region: REGION,
      expiresAt: doc.expiresAt.toISOString(),
    });
  }));

  router.get('/session/results', asyncHandler(async (req, res, next) => {
    const { sessionId } = req.query;
    const user = req.user;
    try {
      const existing = await AWSLivenessVerification.findOne({ sessionId });
      if (!existing) return res.status(404).json({ error: 'Unknown sessionId' });

      const result = await rekognitionLib.getFaceLivenessSessionResults(sessionId);

      const status = result.Status;
      const confidence = typeof result.Confidence === 'number' ? result.Confidence : undefined;

      // Map reference/audit images if Rekognition wrote them to S3
      const key = result.ReferenceImage?.S3Object?.Name;
      const s3Audits = Array.isArray(result.AuditImages) ? result.AuditImages.filter(img => img.S3Object).map(img => img.S3Object.Name) : undefined;

      // Business decision
      let decision = existing.decision;
      if (status === 'SUCCEEDED' && typeof confidence === 'number') {
        decision = confidence >= PASS_THRESHOLD ? 'pass' : 'fail';
      } else if (status === 'FAILED' || status === 'EXPIRED') {
        decision = 'fail';
      } else if (status === 'IN_PROGRESS' || status === 'CREATED') {
        decision = 'unknown';
      }

      // Persist
      existing.status = status;
      if (confidence !== undefined) existing.confidence = confidence;
      if (key) existing.referenceImage = key;
      if (s3Audits.length) existing.auditImages = s3Audits;
      existing.decision = decision;
      await existing.save();

      // If the check is still running, return 202 so the client can poll again
      if (status === 'IN_PROGRESS' || status === 'CREATED') {
        return res.status(202).json({});
      }

      const sendResponse = () => {
        res.json({
          verificationStatus: user.verification.status,
          rejectionReason: user.verification.rejectionReason,
        });
      };

      user.verification.pictures.push(key);
      user.verification.pictureUploadedAt = Date.now();
      user.verification.method = 'aws-liveness';
      user.verification.rejectionReason = undefined;
      user.verification.reVerification = undefined;
      if (!user.events.finished_signup) {
        user.verification.attemptedVerificationDuringSignup = true;
      }

      if (decision == 'fail') {
        await userLib.updateVerificationStatus(user, 'rejected', 'Photo unclear', 'failed aws liveness verification');
        return sendResponse();
      }

      // face comparison
      verificationLib.setFaceComparisonReferenceImage(user, key);
      const faceComparisonResult = await verificationLib.verifyProfilePicture(user);
      await user.save();
      if (faceComparisonResult === 'reject') {
        await userLib.updateVerificationStatus(user, 'rejected', 'Make sure your first profile picture is a picture of you, and only you.', 'failed face comparison');
        return sendResponse();
      }

      // check for banned face
      const bannedFace = await rekognitionLib.findBannedFace(key);
      if (bannedFace) {
        await reportLib.banDueToBannedFaceFound(user, 'aws liveness verification picture', key, bannedFace.Face.ExternalImageId);
        return sendResponse();
      }

      // passed all checks
      await userLib.updateVerificationStatus(user, 'verified', undefined, 'passed aws liveness verification');
      sendResponse();
      await updateUserScore(user, { verification: 1 });
      await socketLib.grantVerifyProfileAward(user);

    } catch (err) {
      if (err.name === 'SessionNotFoundException') {
        return res.status(404).json({ error: 'Session not found or expired' });
      }
      console.error('Get results failed', err);
      return res.status(500).json({ error: 'Failed to fetch liveness results' });
    }
  }));

  return router;
};
