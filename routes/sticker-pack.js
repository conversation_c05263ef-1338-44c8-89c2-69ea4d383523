const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const { applicationError, invalidInputError } = require('../lib/http-errors');
const { iap, processStickerPackPurchase, validateReceiptWithRetry } = require('../lib/iap');
const appStoreJwsValidator = require('../lib/app-store-connect');
const StickerPack = require('../models/sticker-pack');

module.exports = function () {
  router.get('/', asyncHandler(async (req, res, next) => {
    const stickerPacks = await StickerPack.find({}, {
      _id: 0,
      packName: 1,
      stickers: 1,
      productId: 1,
      icon: 1,
      premium: 1,
    });
    return res.json({
      stickerPacks,
    });
  }));

  router.put('/purchase', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (!req.body.receipt && !req.body.transactionJws) {
      return next(invalidInputError(errorMsg));
    }

    const validator = req.body.transactionJws ? appStoreJwsValidator : iap;
    const data = req.body.transactionJws ? req.body.transactionJws : req.body.receipt;

    const validatedData = await validateReceiptWithRetry(user, data, validator, !req.body.transactionJws);
    const purchaseData = validator.getPurchaseData(validatedData, { ignoreCanceled: true });

    console.log(validatedData);
    console.log(purchaseData);

    await processStickerPackPurchase(user, purchaseData)
      .then(() => res.json({}))
      .catch((err) => {
        console.log(err);
        return next(applicationError());
      });
  }));

  return router;
};
