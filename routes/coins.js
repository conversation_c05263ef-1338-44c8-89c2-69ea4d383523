const express = require('express');
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const { DateTime } = require('luxon');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('../lib/http-errors');
const socketLib = require('../lib/socket');
const personalityLib = require('../lib/personality');
const coinsLib = require('../lib/coins');
const chatLib = require('../lib/chat');
const coinsConstants = require('../lib/coins-constants');
const UserMetadata = require('../models/user-metadata');
const StickerPack = require('../models/sticker-pack');
const { translate } = require('../lib/translate');
const { findUser, findUserMetadata, deprecated } = require('../middleware/user');
const { findChat, findApprovedChat } = require('../middleware/chat');
const premiumLib = require('../lib/premium');
const actionLib = require('../lib/action');
const { iap, processCoinPurchase, validateReceiptWithRetry } = require('../lib/iap');
const appStoreJwsValidator = require('../lib/app-store-connect');
const { updateUserScore } = require('../lib/score');

const router = express.Router();
const BoostMetric = require('../models/boost-metric');
const { karmaCrystalCoinConfig } = require('../lib/constants');

async function activateBoost(user, durationMinutes, coinCost) {
  if (user.isBoostActive()) {
    const newExpirationDate = DateTime.fromJSDate(user.boostExpiration).plus({ minutes: durationMinutes }).toJSDate();

    user.boostExpiration = newExpirationDate;
    user.boostDurationMinutes += durationMinutes;
    user.postBoostPopupHandled = undefined;
    user.metrics.numActionsReceivedDuringBoostQuota = user.metrics.numActionsReceivedDuringBoostQuota + 100; 
    await user.save();

    const mostRecentBoost = await BoostMetric.findOne({ user: user._id }).sort('-boostExpiration');
    mostRecentBoost.durationMinutes += durationMinutes;
    mostRecentBoost.coinsSpent += coinCost;
    mostRecentBoost.numBoosts += 1;
    mostRecentBoost.boostExpiration = newExpirationDate;
    await mostRecentBoost.save();

  } else {
    user.boostExpiration = DateTime.utc()
      .plus({ minutes: durationMinutes }).toJSDate();
    user.boostDurationMinutes = durationMinutes;
    user.postBoostPopupHandled = undefined;
    user.metrics.numActionsReceivedDuringBoostQuota = 100;
    await user.save();

    const boostMetric = new BoostMetric({
      user: user._id,
      durationMinutes,
      boostExpiration: user.boostExpiration,
      coinsSpent: coinCost,
      numBoosts: 1,
    });
    await boostMetric.save();
  }

  await updateUserScore(user);

  // deduct coins
  await coinsLib.updateCoins(
    { user: user._id },
    { $inc: { coins: -1 * coinCost } },
    'purchased boost',
  );
}

module.exports = function () {
  router.get('/', findUser, asyncHandler(async (req, res, next) => {
    await req.user.resetCurrentDayMetricsIfNeeded();
    const coinReward = await coinsLib.onLogin(req.user);
    const { coins, rewards } = await coinsLib.getCoins(req.user);

    return res.json({
      coins,
      rewards,
      coinReward: coinReward || undefined,
    });
  }));

  router.get('/products', findUser, (req, res, next) => res.json(coinsLib.getCoinProducts(req.user)));

  router.get('/telepathyCost', deprecated, (req, res, next) => next(notFoundError()));

  router.put('/purchaseCoins', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (!req.body.receipt && !req.body.transactionJws) {
      return next(invalidInputError(errorMsg));
    }

    const validator = req.body.transactionJws ? appStoreJwsValidator : iap;
    const data = req.body.transactionJws ? req.body.transactionJws : req.body.receipt;

    const validatedData = await validateReceiptWithRetry(user, data, validator, !req.body.transactionJws);
    const purchaseData = validator.getPurchaseData(validatedData);

    console.log(validatedData);
    console.log(purchaseData);

    await processCoinPurchase(user, purchaseData, req.body.purchasedFrom, req.body.price, req.body.currency)
      .then(() => res.json({}))
      .catch((err) => {
        console.log(err);
        return next(applicationError());
      });
  }));

  router.put('/purchaseTelepathy', deprecated, (req, res, next) => next(notFoundError()));

  router.put('/shareToSocialMedia', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const coins = await coinsLib.updateCoins(
      {
        user: req.uid,
        shareToSocialMediaRewardReceived: { $ne: true },
      },
      {
        $inc: { coins: coinsConstants.shareToSocialMediaReward },
        $set: { shareToSocialMediaRewardReceived: true },
      },
      'Share to Social Media Reward',
    );

    let coinReward;
    if (coins) {
      coinReward = [{
        caption: translate('Share to Social Media Reward', user.locale),
        rewardAmount: coinsConstants.shareToSocialMediaReward,
        newTotal: coins,
      }];
      socketLib.sendCoinRewards(req.uid, coinReward);
    }

    res.json({
      coinReward,
    });
  }));

  router.put('/rateApp', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const coins = await coinsLib.updateCoins(
      {
        user: req.uid,
        rateAppRewardReceived: { $ne: true },
      },
      {
        $inc: { coins: coinsConstants.getRateAppReward(user) },
        $set: { rateAppRewardReceived: true },
      },
      'Rate App Reward',
    );

    let coinReward;
    if (coins) {
      coinReward = [{
        caption: translate('Rate App Reward', user.locale),
        rewardAmount: coinsConstants.getRateAppReward(user),
        newTotal: coins,
      }];
      socketLib.sendCoinRewards(req.uid, coinReward);
    }

    res.json({
      coinReward,
    });
  }));

  router.put('/detailedReview', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const coins = await coinsLib.updateCoins(
      {
        user: req.uid,
        detailedReviewRewardReceived: { $ne: true },
      },
      {
        $inc: { coins: coinsConstants.getDetailedReviewReward(user) },
        $set: { detailedReviewRewardReceived: true },
      },
      'Detailed Review Reward',
    );

    if (coins) {
      const coinReward = [{
        caption: translate('Testimonial Reward', user.locale),
        rewardAmount: coinsConstants.getDetailedReviewReward(user),
        newTotal: coins,
      }];
      socketLib.sendCoinRewards(req.uid, coinReward);
    }

    res.json({});
  }));

  router.put('/enableTracking', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const coins = await coinsLib.updateCoins(
      {
        user: req.uid,
        enableTrackingRewardReceived: { $ne: true },
      },
      {
        $inc: { coins: coinsConstants.enableTrackingReward },
        $set: { enableTrackingRewardReceived: true },
      },
      'Enable Tracking Reward',
    );

    let coinReward;
    if (coins) {
      coinReward = [{
        caption: translate('Enable Tracking Reward', user.locale),
        rewardAmount: coinsConstants.enableTrackingReward,
        newTotal: coins,
      }];
      socketLib.sendCoinRewards(req.uid, coinReward);
    }

    res.json({});
  }));

  router.put('/howDidYouHearAboutUs', asyncHandler(async (req, res, next) => {
    const howDidYouHearAboutUs = req.body.howDidYouHearAboutUs;
    if (!howDidYouHearAboutUs) {
      return res.json({});
    }

    const user = req.user;
    user.howDidYouHearAboutUs = howDidYouHearAboutUs;
    await user.save();

    const coins = await coinsLib.updateCoins(
      {
        user: req.uid,
        howDidYouHearAboutUsRewardReceived: { $ne: true },
      },
      {
        $inc: { coins: coinsConstants.howDidYouHearAboutUsReward },
        $set: { howDidYouHearAboutUsRewardReceived: true },
      },
      'How Did You Hear About Us Reward',
    );

    let coinReward;
    if (coins) {
      coinReward = [{
        caption: translate('How Did You Hear About Us Reward', user.locale),
        rewardAmount: coinsConstants.howDidYouHearAboutUsReward,
        newTotal: coins,
      }];
      socketLib.sendCoinRewards(req.uid, coinReward);
    }

    res.json({});
  }));

  router.put('/cancelDeleteAccount', asyncHandler(async (req, res, next) => {
    const coins = await coinsLib.updateCoins(
      {
        user: req.uid,
        cancelDeleteAccountRewardReceived: { $ne: true },
      },
      {
        $inc: { coins: coinsConstants.cancelDeleteAccountReward },
        $set: { cancelDeleteAccountRewardReceived: true },
      },
      'Cancel Delete Account Reward',
    );

    res.json({});
  }));

  router.put('/revival', findUser, asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    if (req.user.metrics.revivalUsedAt && moment().diff(req.user.metrics.revivalUsedAt, 'minutes') < 30) {
      // avoid duplicate uses of revival
      return res.json({
        coinsRemaining: req.userMetadata.coins,
      });
    }

    if (req.body.price != coinsConstants.revivalCost) {
      return next(conflictError('The price of this power up has changed.'));
    }

    if (req.userMetadata.coins < coinsConstants.revivalCost) {
      return next(forbiddenError('Insufficient coins'));
    }

    // reset cache
    req.user.preferencesModifiedAt = new Date();
    req.user.metrics.numRevivalUsed += 1;
    req.user.metrics.revivalUsedAt = new Date();
    await req.user.save();

    // deduct coins
    const coins = await coinsLib.updateCoins(
      { user: req.uid },
      { $inc: { coins: -1 * coinsConstants.revivalCost } },
      'revival',
    );

    // revival
    await actionLib.removePassesFromUser(req.user);

    return res.json({
      coinsRemaining: coins,
    });
  }));

  router.put('/boost', asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    if (req.body.price != coinsConstants.boostCost) {
      return next(conflictError('The price of this power up has changed.'));
    }
    if (req.userMetadata.coins < coinsConstants.boostCost) {
      return next(forbiddenError('Insufficient coins'));
    }

    await activateBoost(
      req.user,
      req.user.getBoostDurationMinutes(),
      coinsConstants.boostCost,
    );

    req.user.metrics.numBoostUsed += 1;
    req.user.updateEvents({ use_boost: true })
    await req.user.save();

    return res.json({});
  }));

  router.put('/levelUp', asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    if (req.body.price != coinsConstants.levelUpCost) {
      return next(conflictError('The price of this power up has changed.'));
    }
    if (req.userMetadata.coins < coinsConstants.levelUpCost) {
      return next(forbiddenError('Insufficient coins'));
    }

    await activateBoost(
      req.user,
      coinsConstants.levelUpDurationMinutes,
      coinsConstants.levelUpCost,
    );

    req.user.metrics.numLiftOffUsed += 1;
    await req.user.save();

    return res.json({});
  }));

  router.put('/viewLastSeen', asyncHandler(findUserMetadata), asyncHandler(findApprovedChat), asyncHandler(async (req, res, next) => {

    const user = req.user;
    const coinCost = coinsConstants.viewLastSeenCost;

    if (req.body.price != coinCost) {
      return next(conflictError('The price of this power up has changed.'));
    }
    if (req.userMetadata.coins < coinCost) {
      return next(forbiddenError('Insufficient coins'));
    }

    let readReceipt = req.chat.readReceipts.get(user._id);
    readReceipt.viewLastSeenExpiration = DateTime.utc().plus({ days: 7 }).toJSDate();
    req.chat.readReceipts.set(user._id, readReceipt);
    await req.chat.save();

    // update metrics
    user.metrics.numViewLastSeenPurchased += 1;
    await user.save();

    // deduct coins
    await coinsLib.updateCoins(
      { user: user._id },
      { $inc: { coins: -1 * coinCost } },
      'purchased view last seen',
    );

    return res.json({});
  }));

  router.put('/stickerPack', asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    const user = req.user;
    const coinCost = coinsConstants.stickerPackCost;

    if (req.body.price != coinCost) {
      return next(conflictError('The price has changed.'));
    }
    if (req.userMetadata.coins < coinCost) {
      return next(forbiddenError('Insufficient coins'));
    }

    const productId = req.body.productId;
    const stickerPack = await StickerPack.findOne({ productId: productId, premium: true });
    if (!stickerPack) {
      return next(notFoundError());
    }

    if (user.stickerPackPurchases.includes(productId)) {
      return res.json({});
    }

    user.stickerPackPurchases.push(productId);
    user.metrics.numStickerPackPurchases += 1;
    await user.save();

    // deduct coins
    await coinsLib.updateCoins(
      { user: user._id },
      { $inc: { coins: -1 * coinCost } },
      'purchased sticker pack',
    );

    return res.json({});
  }));

  router.put('/reactivateChat', asyncHandler(findChat), asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    let price = 0;

    // if not god mode, then need to spend coins
    if (!premiumLib.isGodMode(req.user)) {
      if (req.body.price != coinsConstants.reactivateChatCost
          && req.body.price != coinsConstants.reactivateChatCost_v2) {
        return next(conflictError('The price of this power up has changed.'));
      }
      if (req.userMetadata.coins < coinsConstants.reactivateChatCost) {
        return next(forbiddenError('Insufficient coins'));
      }
      price = req.body.price;
    }

    const { chat } = req;

    // no-op if chat is not expired
    if (!chatLib.isChatExpired(chat)) {
      return res.json({
        expirationDate: chatLib.getChatExpirationDate(chat),
      });
    }

    // reactivate it
    chat.lastMessageTime = Date.now();
    await chat.save();

    // deduct coins
    if (price > 0) {
      await coinsLib.updateCoins(
        { user: req.uid },
        { $inc: { coins: -1 * coinsConstants.reactivateChatCost } },
        'reactivated chat',
      );
    }

    return res.json({
      expirationDate: chatLib.getChatExpirationDate(chat),
    });
  }));

  router.get('/karmaCoinReward', asyncHandler(async (req, res, next) => res.json({
    config: karmaCrystalCoinConfig,
  })));

  return router;
};
