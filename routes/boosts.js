const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const httpErrors = require('../lib/http-errors');
const { iap, processBoostPurchase, validateReceiptWithRetry } = require('../lib/iap');
const appStoreJwsValidator = require('../lib/app-store-connect');
const { activateBoostByPurchase } = require('../lib/boost')

module.exports = function () {
  router.put('/purchase', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (!req.body.receipt && !req.body.transactionJws) {
      return next(httpErrors.invalidInputError(errorMsg));
    }

    const validator = req.body.transactionJws ? appStoreJwsValidator : iap;
    const data = req.body.transactionJws ? req.body.transactionJws : req.body.receipt;

    const validatedData = await validateReceiptWithRetry(user, data, validator, !req.body.transactionJws);
    const purchaseData = validator.getPurchaseData(validatedData);

    console.log('validatedData :', validatedData);
    console.log('purchaseData :', purchaseData);

    await processBoostPurchase(user, purchaseData, req.body.price, req.body.currency)
      .then(() => res.json({}))
      .catch((err) => {
        console.log(err);
        return next(httpErrors.applicationError());
      });
  }));

  router.put('/use', asyncHandler(async (req, res, next) => {
    const numBoosts = (req.user.numBoosts ?? 0) + (req.user.numBoostsFree ?? 0);
    if (numBoosts < 1) {
      return next(httpErrors.forbiddenError('No boost remaining'));
    }

    const activateReturn = await activateBoostByPurchase(
      req.user,
      req.user.getBoostDurationMinutes(),
    );

    req.user.metrics.numBoostUsed += 1;
    req.user.updateEvents({ use_boost: true });
    await req.user.save();

    return res.json({ ...activateReturn });
  }));

  router.put('/activityToken', asyncHandler(async (req, res, next) => {
    const user = req.user;

    user.boostActivityToken = req.body.activityToken;
    await user.save();

    return res.json({});
  }));

  return router;
}
